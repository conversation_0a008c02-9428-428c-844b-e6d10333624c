import React, { Suspense, useState } from "react";
import use<PERSON><PERSON> from "swr";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { LiveIndicator } from "@/components/LiveIndicator";
import { ExportButton } from "@/components/ExportButton";

const TopIntentsBarChart = React.lazy(() => import("./components/TopIntentsBarChart"));
const TopicTrendsLineChart = React.lazy(() => import("./components/TopicTrendsLineChart"));
const SentimentPieChart = React.lazy(() => import("./components/SentimentPieChart"));
const TopQuestionsTable = React.lazy(() => import("./components/TopQuestionsTable"));

const fetcher = (url: string) => fetch(url).then(res => res.json());

const KPI_CARDS = [
  { key: "total_queries", label: "Total Queries Today" },
  { key: "avg_sentiment", label: "Avg. Sentiment" },
  { key: "active_users", label: "Active Users" },
  { key: "unique_questions", label: "Unique Questions" },
];

const INSIGHTS_TOPICS = ["Leave Balance", "Payslip", "Attendance"];

const Insights: React.FC = () => {
  const { data, error, isLoading } = useSWR("/api/chat-analytics/live", fetcher, { refreshInterval: 30000 });
  const [tab, setTab] = useState("intents");

  // Extract analytics data
  const topIntents = data?.top_intents || [];
  const trendingTopics = data?.trending_topics || [];
  const sentimentDistribution = data?.sentiment_distribution || [];
  const topQuestions = data?.top_questions || [];

  return (
    <div className="space-y-6">
      {/* KPIs */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Insights</h1>
          <p className="text-muted-foreground">Live analytics from chatbot users</p>
        </div>
        <div className="flex items-center gap-3">
          <LiveIndicator />
          <ExportButton
            onExportPDF={() => {}}
            onExportCSV={() => {}}
            onExportPNG={() => {}}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full rounded-2xl" />
          ))
        ) : error ? (
          <div className="col-span-4 text-red-500 p-4">Failed to load KPIs</div>
        ) : (
          KPI_CARDS.map(card => (
            <Card key={card.key} className="rounded-2xl border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
              <CardContent className="p-6">
                <div className="flex flex-col gap-2">
                  <span className="text-sm font-medium text-muted-foreground">{card.label}</span>
                  <span className="text-2xl font-bold mt-1">{data?.[card.key] ?? "-"}</span>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
      {/* Tabs for analytics sections */}
      <Tabs value={tab} onValueChange={setTab} className="w-full">
        <TabsList className="mb-4 flex flex-wrap gap-2">
          <TabsTrigger value="intents">Top Intents</TabsTrigger>
          <TabsTrigger value="topics">Trending Topics</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment Distribution</TabsTrigger>
          <TabsTrigger value="questions">Top Questions</TabsTrigger>
        </TabsList>
        <TabsContent value="intents">
          <Card>
            <CardHeader>
              <CardTitle>Top Intents</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<Skeleton className="h-80 w-full" />}>
                <TopIntentsBarChart topIntents={topIntents} loading={isLoading} error={error} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="topics">
          <Card>
            <CardHeader>
              <CardTitle>Trending Topics</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<Skeleton className="h-80 w-full" />}>
                <TopicTrendsLineChart trendingTopics={trendingTopics} topics={INSIGHTS_TOPICS} loading={isLoading} error={error} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="sentiment">
          <Card>
            <CardHeader>
              <CardTitle>Sentiment Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<Skeleton className="h-80 w-full" />}>
                <SentimentPieChart sentimentDistribution={sentimentDistribution} loading={isLoading} error={error} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="questions">
          <Card>
            <CardHeader>
              <CardTitle>Top Questions</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<Skeleton className="h-80 w-full" />}>
                <TopQuestionsTable topQuestions={topQuestions} loading={isLoading} error={error} />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Insights; 