/**
 * Remove Welcome Message After Login
 * This script removes the welcome message after a user logs in
 */

// Run immediately before DOM content loads to prevent welcome message flash
(function() {
    try {
        // Check if user is logged in
        const isLoggedIn = !!localStorage.getItem('user_data');

        if (isLoggedIn) {
            // Add CSS to immediately hide pre-login welcome containers only
            const style = document.createElement('style');
            style.id = 'hide-welcome-style'; // Add ID for easy removal
            style.textContent = `
                html.logged-in .pre-login-welcome-container,
                body.logged-in .pre-login-welcome-container {
                    display: none !important;
                }
                .pre-login-welcome-container {
                    transition: none !important;
                    animation: none !important;
                }
            `;
            document.head.appendChild(style);
        }
    } catch (error) {
        console.error('Error in immediate welcome message removal:', error);
    }
})();

document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    const isLoggedIn = !!localStorage.getItem('user_data');

    if (isLoggedIn) {
        // Remove pre-login welcome message if user is logged in
        removePreLoginWelcomeMessage();

        // Also listen for login events to remove pre-login welcome message
        window.addEventListener('user-logged-in', () => {
            removePreLoginWelcomeMessage();
            setTimeout(removePreLoginWelcomeMessage, 100);
            setTimeout(removePreLoginWelcomeMessage, 500);
        });

        // Set up a MutationObserver to catch any pre-login welcome containers that might be added dynamically
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            // Check if this is a pre-login welcome container or contains one
                            if (node.classList && node.classList.contains('pre-login-welcome-container')) {
                                node.style.display = 'none';
                                node.remove();
                            } else if (node.querySelectorAll) {
                                const preLoginWelcomeContainers = node.querySelectorAll('.pre-login-welcome-container');
                                preLoginWelcomeContainers.forEach(container => {
                                    container.style.display = 'none';
                                    container.remove();
                                });
                            }
                        }
                    }
                }
            });
        });

        // Start observing the document
        observer.observe(document.documentElement, { childList: true, subtree: true });

        // Listen for logout events to stop the observer
        window.addEventListener('user-logged-out', () => {
            console.log('User logged out - stopping pre-login welcome container observer');
            observer.disconnect();
        });
    }

    // Function to remove pre-login welcome message
    function removePreLoginWelcomeMessage() {
        // Find all pre-login welcome containers (there might be more than one)
        const preLoginWelcomeContainers = document.querySelectorAll('.pre-login-welcome-container');

        if (preLoginWelcomeContainers.length > 0) {
            preLoginWelcomeContainers.forEach(container => {
                container.style.display = 'none'; // Hide immediately
                container.remove(); // Then remove from DOM
                console.log('Pre-login welcome container removed after login');
            });

            // Add the welcome-removed class to body
            document.body.classList.add('welcome-removed');

            // Reposition the chat input to the bottom
            const chatInputContainer = document.querySelector('.chat-input-container');
            if (chatInputContainer) {
                chatInputContainer.style.position = 'fixed';
                chatInputContainer.style.top = 'auto';
                chatInputContainer.style.bottom = '24px';
                chatInputContainer.style.transform = 'translate(-50%, 0)';
                chatInputContainer.style.left = '50%';
                chatInputContainer.style.marginLeft = '0';
                chatInputContainer.style.right = 'auto';
            }
        }
    }

    // Run immediately and then again after a short delay
    if (isLoggedIn) {
        removePreLoginWelcomeMessage();
        setTimeout(removePreLoginWelcomeMessage, 100);
        setTimeout(removePreLoginWelcomeMessage, 500);
    }

    // Listen for logout events to clean up and allow welcome containers to show again
    window.addEventListener('user-logged-out', () => {
        console.log('User logged out - cleaning up welcome container hiding');

        // Remove the injected CSS that hides welcome containers
        const hideWelcomeStyle = document.getElementById('hide-welcome-style');
        if (hideWelcomeStyle) {
            hideWelcomeStyle.remove();
            console.log('Removed hide-welcome-style CSS');
        }

        // Remove the welcome-removed class from body
        document.body.classList.remove('welcome-removed');
        console.log('Removed welcome-removed class from body');
    });
});
