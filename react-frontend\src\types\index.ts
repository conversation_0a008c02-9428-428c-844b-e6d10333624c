export interface User {
  id: string;
  email: string;
  fullName: string;
  employeeId?: string;
  isLoggedIn: boolean;
}

export interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  files?: FileAttachment[];
  sender?: 'user' | 'assistant';
}

export interface FileAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
  preview?: string;
  file?: File;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  isArchived?: boolean;
}

export interface EscalationForm {
  hrPerson: string;
  issueType: 'policy' | 'benefits' | 'workplace' | 'compensation' | 'other';
  issueDescription: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface ThemeMode {
  mode: 'light' | 'dark' | 'system';
}

export interface AppSettings {
  theme: ThemeMode;
  language: string;
  notifications: boolean;
}

export interface SpeechRecognitionState {
  isListening: boolean;
  isSupported: boolean;
  transcript: string;
  error?: string;
}

export interface ModalState {
  login: boolean;
  register: boolean;
  settings: boolean;
  voice: boolean;
  filePreview: boolean;
  escalation: boolean;
  archivedChats: boolean;
  twoFA: boolean;
}

export interface SidebarState {
  isCollapsed: boolean;
  isVisible: boolean;
}
