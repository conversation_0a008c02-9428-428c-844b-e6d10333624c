"""
Improved HR Assistant Prompt Template with better formatting and structure.
"""

from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate
)
from typing import List, Dict, Any, Optional
import json

from ..utils.logger import get_logger

logger = get_logger(__name__)


def create_hr_assistant_prompt(language: str = "English", 
                              response_mode: str = "auto",
                              include_reasoning: bool = False) -> ChatPromptTemplate:
    """
    Create an improved prompt template for the Ziantrix HR Assistant.
    
    Args:
        language: Language to use in the prompt
        response_mode: Response mode ("auto", "concise", "detailed", "step_by_step")
        include_reasoning: Whether to include reasoning
        
    Returns:
        ChatPromptTemplate configured for Ziantrix HR Assistant
    """
    
    system_prompt = create_system_prompt(language, response_mode)
    
    return ChatPromptTemplate.from_messages([
        SystemMessagePromptTemplate.from_template(system_prompt),
        MessagesPlaceholder(variable_name="history"),
        SystemMessagePromptTemplate.from_template("HR Document Context:\n{context}"),
        HumanMessagePromptTemplate.from_template("{query}")
    ])


def create_system_prompt(language: str, response_mode: str) -> str:
    """Create a clean, focused system prompt."""
    
    return f"""You are Zia, the AI HR Assistant for Ziantrix Technology Solutions.

## Core Rules:
1. **Document-Only Information**: Only use information from the provided HR Document Context
2. **Exact Accuracy**: Use precise numbers, dates, and requirements as stated in documents
3. **Clear Attribution**: Reference specific policy sections when available
4. **Professional Tone**: Maintain a helpful, professional demeanor

## Response Format:
- Start with a brief, direct answer
- Use clear headings and bullet points for organization
- Include exact figures and requirements from documents
- End with offer to help further if needed

## When Information is Missing:
- State clearly: "This information is not available in the current policy documents"
- Suggest contacting HR for clarification
- Never guess or use general knowledge

## Response Style: {get_response_mode_instructions(response_mode)}

## Language: {language}

Format your responses with:
- **Clear headings** for different topics
- **Bullet points** for lists and requirements
- **Bold text** for important numbers and dates
- **Proper spacing** between sections

Keep responses helpful, accurate, and well-organized."""


def get_response_mode_instructions(mode: str) -> str:
    """Get response mode instructions."""
    
    modes = {
        "concise": "Provide brief, focused answers with key facts only",
        "detailed": "Provide comprehensive information with full explanations",
        "step_by_step": "Break down procedures into clear, numbered steps",
        "auto": "Adapt response length based on query complexity"
    }
    
    return modes.get(mode, modes["auto"])


def create_leave_policy_template() -> str:
    """Create a specific template for leave policy responses."""
    
    return """
## {leave_type} Policy

**Eligibility:** {eligibility}
**Entitlement:** {entitlement}
**Key Requirements:**
{requirements}

**Important Notes:**
{notes}

---
*Need help with leave application or have questions? Feel free to ask!*
"""


def create_formatted_response_template(response_type: str) -> str:
    """Create formatted response templates for different types of queries."""
    
    templates = {
        "leave_policy": """
## Leave Policy Overview

{policy_details}

**Available Leave Types:**
{leave_types}

**Need Help?**
I can provide specific details about any leave type or help you understand application procedures.
""",
        
        "leave_balance": """
## Your Leave Balance

{balance_info}

**Next Steps:**
{next_steps}
""",
        
        "policy_general": """
## {policy_name}

{policy_content}

**Key Points:**
{key_points}

**Questions?** Feel free to ask for clarification on any aspect of this policy.
""",
        
        "procedure": """
## {procedure_name}

**Steps to Follow:**
{steps}

**Required Documents:**
{documents}

**Timeline:** {timeline}

**Need assistance?** I'm here to help with any questions about this process.
"""
    }
    
    return templates.get(response_type, templates["policy_general"])


def format_leave_types(leave_data: List[Dict[str, Any]]) -> str:
    """Format leave types data into a readable structure."""
    
    formatted_output = []
    
    for leave in leave_data:
        leave_info = f"""
**{leave.get('type', 'Unknown Leave Type')}**
- **Eligibility:** {leave.get('eligibility', 'Not specified')}
- **Entitlement:** {leave.get('entitlement', 'Not specified')}
- **Requirements:** {leave.get('requirements', 'None specified')}
"""
        formatted_output.append(leave_info)
    
    return "\n".join(formatted_output)


def create_error_response_template() -> str:
    """Create template for error responses."""
    
    return """
## Information Not Available

I don't have the specific information you're looking for in the current policy documents.

**What you can do:**
- Contact HR directly for this information
- Check the employee portal for additional resources
- Rephrase your question if you're looking for something specific

**I'm here to help** with any information that's available in the policy documents.
"""


def validate_response_format(response: str) -> Dict[str, Any]:
    """Validate that response follows proper formatting guidelines."""
    
    validation = {
        "has_headers": "##" in response or "**" in response,
        "has_structure": "\n" in response and len(response.split("\n")) > 2,
        "reasonable_length": 50 <= len(response) <= 2000,
        "has_attribution": any(phrase in response.lower() for phrase in [
            "according to", "as stated", "policy", "document"
        ]),
        "professional_tone": not any(word in response.lower() for word in [
            "i think", "maybe", "probably", "i guess"
        ])
    }
    
    validation["is_well_formatted"] = all([
        validation["has_headers"],
        validation["has_structure"],
        validation["reasonable_length"]
    ])
    
    return validation


def detect_query_type(query: str) -> str:
    """Detect the type of query to apply appropriate formatting."""
    
    query_lower = query.lower()
    
    if any(word in query_lower for word in ["leave policy", "types of leave", "leave types"]):
        return "leave_policy"
    
    if any(word in query_lower for word in ["balance", "remaining", "available"]):
        return "leave_balance"
    
    if any(word in query_lower for word in ["how to", "process", "procedure", "apply"]):
        return "procedure"
    
    if any(word in query_lower for word in ["policy", "rule", "guideline"]):
        return "policy_general"
    
    return "general"


def create_context_prompt() -> str:
    """Create a focused context prompt."""
    
    return """
Based on the HR Document Context provided above, please answer the user's question with:

1. **Accurate Information**: Only use facts from the provided context
2. **Clear Structure**: Use headings, bullet points, and proper formatting
3. **Professional Tone**: Be helpful and informative
4. **Specific Details**: Include exact numbers, dates, and requirements
5. **Proper Attribution**: Reference policy sections when available

If the information isn't in the context, clearly state this and suggest contacting HR.
"""


# Template constants with better formatting
LEAVE_BALANCE_TEMPLATE = """
## Your Leave Balance

**Current Balance:**
• **Earned Leave:** {earned_leave} days
• **Sick Leave:** {sick_leave} days  
• **Casual Leave:** {casual_leave} days

**Need Help?**
I can assist with leave applications or explain usage policies.
"""

POLICY_NOT_FOUND_TEMPLATE = """
## Information Not Available

The specific policy you're asking about is not available in the current HR documents.

**What you can do:**
- Contact HR directly for this information
- Check the employee portal for additional policies
- Ask me about other available policies

**Available Topics:**
I can help with leave policies, general employment policies, and procedures that are documented.
"""

# Export functions
__all__ = [
    'create_hr_assistant_prompt',
    'create_system_prompt',
    'create_formatted_response_template',
    'format_leave_types',
    'validate_response_format',
    'detect_query_type',
    'LEAVE_BALANCE_TEMPLATE',
    'POLICY_NOT_FOUND_TEMPLATE'
]