import os
import re
import time
import asyncio
import traceback
from typing import List, Dict, Any
from langchain_groq import <PERSON>tGroq
from langchain.schema import HumanMessage, AIMessage

from ..utils.logger import get_logger
from ..retrieval.context_builder import ContextBuilder
from ..conversation.history_manager import HistoryManager
from ..conversation.language_detector import detect_language
from .prompt_templates import (
    create_hr_assistant_prompt
)
from ..config import (
    GROQ_API_KEY, LLM_MODEL_NAME, HR_EMAILS, ENABLE_EMAIL_ESCALATION,
    INTENT_CLASSIFIER_CONFIDENCE_THRESHOLD
)
from ..utils.api_status import API<PERSON><PERSON><PERSON><PERSON>he<PERSON>
from ..utils.email_service import EmailService
from ..intent.intent_classifier import IntentClassifier # Ensure this import is present
from ..ner.entity_extractor import EntityExtractor     # Ensure this import is present

from ..document_processing.version_control import DocumentVersionControl

logger = get_logger(__name__)

GREETING_KEYWORDS = ["hi", "hello", "hey", "good morning", "good afternoon", "good evening", "howdy"]

def is_pure_greeting(text: str) -> bool:
    return any(re.fullmatch(rf"(?i){kw}[!. ]*", text.strip()) for kw in GREETING_KEYWORDS)

def sanitize_input(text: str) -> str:
    return re.sub(r"[\n\r\t]+", " ", text.strip())

def contains_sensitive_info(text: str) -> bool:
    return bool(re.search(r"(pan|aadhaar|salary|bank account|ifsc|dob|ssn|passport)", text, re.IGNORECASE))


class ChainBuilder:
    def __init__(self, model_name=LLM_MODEL_NAME, api_key=GROQ_API_KEY,
                 context_builder=None, history_manager=None, email_service=None):

        if not api_key:
            raise ValueError("GROQ_API_KEY is required but not set")
        if not model_name:
            raise ValueError("LLM_MODEL_NAME is required but not set")

        logger.info(f"Initializing ChainBuilder with model: {model_name}")

        self.model_name = model_name
        self.api_key = api_key
        self.api_status_checker = APIStatusChecker()
        self.context_builder = context_builder or ContextBuilder(min_content_length=30)
        self.history_manager = history_manager or HistoryManager()
        self.email_service = email_service or EmailService()
        self.intent_classifier = IntentClassifier() # Initialized here
        self.entity_extractor = EntityExtractor()     # Initialized here

        self.version_control = DocumentVersionControl()
        self.hr_emails = HR_EMAILS
        self.enable_email_escalation = ENABLE_EMAIL_ESCALATION

        self.llm = ChatGroq(
            model=model_name,
            groq_api_key=api_key,
            max_tokens=750,
            temperature=0.1,
            timeout=60,
            model_kwargs={
                "top_p": 0.9,
                "frequency_penalty": 0.2,
                "presence_penalty": 0.6,
            },
        )

    def format_response(self, text: str) -> str:
        if not text.strip():
            return "I'm sorry, I couldn't find a helpful response."

        paragraphs = text.split("\n\n")
        formatted = []

        for para in paragraphs:
            lines = para.splitlines()
            if any(para.lower().startswith(k.lower()) for k in ["What is", "Causes of", "Symptoms of", "Types of", "Prevention of"]):
                formatted.append(f"### {para}")
            elif all(line.strip().startswith(("-", "*")) for line in lines if line.strip()):
                formatted.extend([line.strip() for line in lines])
            elif all(line.strip()[:2].isdigit() and line.strip()[2] == '.' for line in lines if line.strip()):
                formatted.extend(lines)
            else:
                formatted.append(para)

        return "\n\n".join(formatted)

    def format_bullets(self, text: str) -> str:
        import re
        lines = text.split('\n')
        formatted = []
        in_list = False
        for idx, line in enumerate(lines):
            line = line.strip()
            if not line:
                if in_list:
                    formatted.append('</ul>')
                    in_list = False
                continue  # skip extra blank lines
            # Heading: line is all bold (starts and ends with **, and not a bullet)
            if re.match(r'^\*\*.+\*\*$', line) and not re.match(r'^(\* |- |• )', line):
                if in_list:
                    formatted.append('</ul>')
                    in_list = False
                # Use <h3> for the first heading, <strong> for others
                if idx == 0:
                    formatted.append(f'<h3>{line.strip("*")}</h3>')
                else:
                    formatted.append(f'<strong>{line.strip("*")}</strong>')
                continue
            # Real bullet: starts with '* ', '- ', or '• '
            if re.match(r'^(\* |- |• )', line):
                content = re.sub(r'^(\* |- |• )', '', line).strip()
                if not in_list:
                    formatted.append('<ul>')
                    in_list = True
                formatted.append(f'<li>{content}</li>')
                continue
            # Not a bullet or heading
            if in_list:
                formatted.append('</ul>')
                in_list = False
            formatted.append(line)
        if in_list:
            formatted.append('</ul>')
        return '\n'.join(formatted)

    async def run_chain(self, query: str, device_id: str, files_info: List[Dict[str, Any]] = None, retry_count: int = 0) -> Dict[str, Any]:
        logger.debug(f"[run_chain] Started for query: '{query}', device_id: {device_id}")
        
        # Initialize final_result at the beginning to avoid UnboundLocalError
        final_result = {
            "content": "I'm sorry, I couldn't process your request.",
            "language": "en",
            "sources": [],
            "error": {"type": "ProcessingError", "message": "Failed to process query"}
        }
        
        try:
            query = sanitize_input(query)
            logger.debug(f"[run_chain] Sanitized query: '{query}'")

            if not self.api_status_checker.is_groq_operational():
                logger.warning("[run_chain] Groq API is not operational.")
                return {
                    "content": "The language model service is currently unavailable. Please try again later.",
                    "language": "en",
                    "sources": [],
                    "error": {"type": "ServiceUnavailable"}
                }
            logger.debug("[run_chain] Groq API is operational.")

            language = detect_language(query)
            logger.debug(f"[run_chain] Detected language: {language}")

            try:
                # Use the intent classifier
                intent_result = self.intent_classifier.classify_intent(query)
                intent = intent_result.get("intent", "unknown")
                confidence = intent_result.get("confidence", 0.0)
                logger.debug(f"[run_chain] Intent classified: {intent}, confidence: {confidence}")
            except Exception as e:
                logger.warning(f"[run_chain] Intent classification failed: {e}")
                intent, confidence = "unknown", 0.0

            if is_pure_greeting(query):
                logger.debug("[run_chain] Pure greeting detected, returning canned response.")
                return {
                    "content": "Hello! How can I assist you with HR-related matters today?",
                    "language": language,
                    "sources": [],
                    "intent": "greeting",
                    "intent_confidence": 1.0,
                    "entities": []
                }

            if contains_sensitive_info(query):
                logger.warning("[run_chain] Sensitive information detected in user input.")

            try:
                # Use the entity extractor
                entities = self.entity_extractor.extract_entities(query)
                logger.debug(f"[run_chain] Extracted entities: {entities}")
            except Exception as e:
                logger.warning(f"[run_chain] Entity extraction failed: {e}")
                entities = []

            if files_info:
                logger.debug(f"[run_chain] Processing files: {files_info}")
                for file in files_info:
                    path = file.get("path")
                    if path and self.version_control.check_version(path):
                        self.version_control.reindex_document(path)
                        logger.debug(f"[run_chain] Reindexed document: {path}")

            try:
                history_items = self.history_manager.get_history(device_id)[-5:]
                history_msgs = [
                    item for h in history_items
                    for item in [HumanMessage(content=f"User: {h['user_query']}"), AIMessage(content=h['assistant_response'])]
                ]
                logger.debug(f"[run_chain] Retrieved history items: {len(history_items)}")
            except Exception as e:
                logger.warning(f"[run_chain] History retrieval failed: {e}")
                history_msgs = []

            try:
                context_result = await self.context_builder.build_context(query, max_tokens=400,files_info=files_info)
                # Fix for the NoneType error - ensure context_result is not None
                if context_result is not None:
                    context = context_result.context
                    sources = context_result.sources
                else:
                    context, sources = "", []
                logger.debug(f"[run_chain] Context built. Sources: {sources}")
            except Exception as e:
                logger.warning(f"[run_chain] Context building failed: {e}")
                context, sources = "", []

            try:
                # Advanced prompt engineering: detect response mode and reasoning needs
                response_mode = "auto"
                include_reasoning = True
                
                logger.debug(f"[run_chain] Using response_mode: {response_mode}, include_reasoning: {include_reasoning}")
                
                prompt_template = create_hr_assistant_prompt(
                    language=language,
                    response_mode=response_mode,
                    include_reasoning=include_reasoning
                )
                inputs = {
                    "query": query,
                    "context": context,
                    "history": history_msgs
                }
                response = await asyncio.to_thread(prompt_template.invoke, inputs)
                logger.debug(f"[run_chain] Advanced prompt created with mode: {response_mode}")
            except Exception as e:
                logger.error(f"[run_chain] Prompt creation failed: {e}")
                return await self._error("PromptCreationError", e, retry_count)

            try:
                start = time.time()
                response = await asyncio.to_thread(self.llm.invoke, response)
                elapsed = time.time() - start
                raw_response = getattr(response, "content", str(response)).strip()
                logger.debug(f"[run_chain] LLM invoked. Raw response length: {len(raw_response)}")
            except Exception as e:
                logger.error(f"[run_chain] LLM invocation failed: {e}")
                if retry_count < 2:
                    logger.warning(f"[run_chain] Retrying LLM invocation ({retry_count + 1}/2).")
                    await asyncio.sleep(2 ** retry_count)
                    return await self.run_chain(query, device_id, files_info, retry_count + 1)
                return await self._error(type(e).__name__, e, retry_count)

            needs_escalation = "[ESCALATE_TO_HR]" in raw_response
            if needs_escalation:
                logger.debug("[run_chain] Escalation tag detected.")
                raw_response = raw_response.replace("[ESCALATE_TO_HR]", "").strip()
                if self.enable_email_escalation and self.hr_emails:
                    raw_response += "\n\n**Would you like me to escalate this to HR?**"

            try:
                formatted_response = self.format_response(raw_response)
                logger.debug(f"[run_chain] Response formatted. Length: {len(formatted_response)}")
                

            except Exception as e:
                logger.warning(f"[run_chain] Response formatting failed: {e}")
                formatted_response = raw_response

            # Apply bullet formatting
            formatted_response = self.format_bullets(formatted_response)

            final_result = {
                "content": formatted_response,
                "language": language,
                "sources": sources,
                "response_time": elapsed,
                "escalated": needs_escalation,
                "intent": intent,            # Included intent
                "intent_confidence": confidence, # Included intent confidence
                "entities": entities,        # Included entities
                "response_mode": response_mode,  # Advanced prompt mode used
                "include_reasoning": include_reasoning  # Whether reasoning was included
            }
            logger.debug(f"[run_chain] Final result prepared: {final_result.keys()}")

            logger.debug("[run_chain] Returning final result.")
            return final_result

        except Exception as e:
            logger.error(f"[run_chain] Unhandled exception in run_chain: {e}")
            logger.error(f"AttributeError: {e}")
            return await self._error(type(e).__name__, e, retry_count)

    async def _error(self, error_type: str, error: Exception, retry: int = 0) -> Dict[str, Any]:
        logger.error(f"{error_type}: {error}")
        trace = traceback.format_exc()

        message_map = {
            "timeout": "The request timed out. Please try again.",
            "api key": "Invalid API key provided. Please check your configuration.",
            "rate limit": "Rate limit exceeded. Please try again shortly.",
            "context length": "The input is too long. Please shorten your query."
        }

        friendly_msg = "I'm sorry, something went wrong. Please try again."
        for keyword, msg in message_map.items():
            if keyword in str(error).lower():
                friendly_msg = msg
                break

        return {
            "content": friendly_msg,
            "language": "en",
            "sources": [],
            "error": {
                "type": error_type,
                "message": str(error),
                "traceback": trace,
                "retry_attempted": retry > 0
            }
        }

    def get_vector_database_count(self) -> int:
        """Get the number of vectors in the vector database."""
        try:
            # Fix for Qdrant vector store - use proper method to get count
            if hasattr(self.context_builder, 'vector_search') and hasattr(self.context_builder.vector_search, 'vector_store'):
                vector_store = self.context_builder.vector_search.vector_store
                
                # For Qdrant, use the client to get collection info
                if hasattr(vector_store, 'client') and hasattr(vector_store, 'collection_name'):
                    try:
                        collection_info = vector_store.client.get_collection(vector_store.collection_name)
                        return collection_info.points_count if collection_info else 0
                    except Exception as e:
                        logger.debug(f"Could not get collection info from Qdrant: {e}")
                        return 0
                
                # Fallback for other vector stores with index attribute
                elif hasattr(vector_store, 'index') and vector_store.index:
                    return getattr(vector_store.index, 'ntotal', 0)
                
                # Another fallback - try to get count via similarity search
                try:
                    # Try a dummy search to see if there are any documents
                    results = vector_store.similarity_search("test", k=1)
                    return len(results) if results else 0
                except:
                    return 0
                
            return 0
        except Exception as e:
            logger.error(f"Error getting vector database count: {e}")
            return 0

    def make_response_concise(self, response: str) -> str:
        """Make a response more concise by extracting key information. Fallback to longer summary if only a heading or generic line is returned."""
        try:
            from ..config import MAX_RESPONSE_WORDS
            
            # If response is already short, return as is
            if len(response.split()) <= MAX_RESPONSE_WORDS:
                return response
                
            # Extract bullet points or key facts
            lines = response.split('\n')
            concise_parts = []
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                # Keep important bullet points and key information
                if line.startswith(('•', '-', '*', '1.', '2.', '3.')) and len(line) < 100:
                    concise_parts.append(line)
                elif line.startswith('**') and line.endswith('**'):  # Bold headers
                    concise_parts.append(line)
                elif len(line.split()) <= 20 and any(word in line.lower() for word in ['policy', 'days', 'hours', 'required', 'allowed', 'prohibited', 'attire', 'dress', 'friday', 'men', 'women']):
                    concise_parts.append(line)
            # If we found 2 or more key points, return them
            if len(concise_parts) >= 2:
                return '\n'.join(concise_parts[:4])  # Limit to 4 key points
            # If only a heading or generic line is found, fallback to a longer summary
            if len(concise_parts) == 1 and (concise_parts[0].lower().startswith('dress code') or len(concise_parts[0].split()) < 8):
                # Fallback: return first 150 words
                words = response.split()
                return ' '.join(words[:150]) + ('...' if len(words) > 150 else '')
            # Otherwise, return first sentence or first MAX_RESPONSE_WORDS words
            sentences = response.split('.')
            if sentences:
                first_sentence = sentences[0].strip()
                if len(first_sentence.split()) <= 30:
                    return first_sentence + '.'
            # Fallback: return first MAX_RESPONSE_WORDS words
            words = response.split()
            return ' '.join(words[:MAX_RESPONSE_WORDS]) + ('...' if len(words) > MAX_RESPONSE_WORDS else '')
        except Exception as e:
            logger.warning(f"Error making response concise: {e}")
            return response

    def summarize(self, document: str) -> str:
        """Generate a concise, actionable summary for the given document using the LLM."""
        try:
            if not document or len(document.strip()) < 20:
                return "Document is too short to summarize."
            prompt = (
                "You are an expert HR assistant. Summarize the following document in 3-5 concise bullet points. "
                "Focus on the most important policies, actions, or facts. Avoid generic statements. "
                "Limit your summary to 150 words.\n\n"
                f"Document:\n{document}\n\nSummary (bullet points):"
            )
            response = self.llm([HumanMessage(content=prompt)])
            if hasattr(response, 'content'):
                return response.content.strip()
            return str(response).strip()
        except Exception as e:
            return f"Error generating summary: {e}"


# Add main block for standalone testing
if __name__ == "__main__":
    async def test():
        cb = ChainBuilder()
        result = await cb.run_chain("tell me about moonlighting policy?", device_id="test-device-id")
        print("\n\n=== Chain Output ===")
        for k, v in result.items():
            print(f"{k}: {v}\n")

    asyncio.run(test())