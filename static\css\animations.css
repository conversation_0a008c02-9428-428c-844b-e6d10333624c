/* 
 * Animations and Transitions
 * Contains keyframes, animations, transitions, and transform effects
 */

/* Base Transition Classes */
.transition-all {
    transition: all 0.2s ease;
}

.transition-colors {
    transition: background-color 0.2s ease, color 0.2s ease;
}

.transition-transform {
    transition: transform 0.2s ease;
}

/* Keyframe Animations */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: scale(0.95);
    }
    to { 
        opacity: 1; 
        transform: scale(1);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { 
        transform: translate(0,0); 
    }
    40%, 43% { 
        transform: translate(0, -5px); 
    }
    78% { 
        transform: translate(0, -2px); 
    }
}

@keyframes quick-highlight {
    0% { background-color: transparent; }
    50% { background-color: rgba(0, 123, 255, 0.3); }
    100% { background-color: transparent; }
}

@keyframes rotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.6;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes recording {
    0%, 100% {
        height: 3px;
        transform: scaleY(1);
    }
    50% {
        height: 15px;
        transform: scaleY(1.5);
    }
}

@keyframes documentPulse {
    0% { opacity: 0.9; }
    50% { opacity: 1; }
    100% { opacity: 0.9; }
}

@keyframes documentPulseDark {
    0% { opacity: 0.9; }
    50% { opacity: 1; }
    100% { opacity: 0.9; }
}

@keyframes subtleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-2px);
    }
}

/* Rotating Gradient Border Animation */
@keyframes rotateBorderGradient {
    0% {
        background: linear-gradient(#ffffff, #ffffff) padding-box,
                    conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
    }
    100% {
        background: linear-gradient(#ffffff, #ffffff) padding-box,
                    conic-gradient(from 360deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
    }
}

@keyframes rotateGlow {
    0% {
        transform: rotate(0deg) !important;
    }
    100% {
        transform: rotate(360deg) !important;
    }
}

/* Gradient Border Animation for Input with Text */
.chatgpt-input-wrapper.has-text,
html body .pre-login-bottom-input-container .chatgpt-input-wrapper.has-text,
html body .pre-login-container .chatgpt-input-wrapper.has-text,
footer.chat-input-container .chatgpt-input-wrapper.has-text,
.chat-input-container .chatgpt-input-wrapper.has-text,
.chat-input-form .chatgpt-input-wrapper.has-text,
html.logged-in .chat-input-container .chatgpt-input-wrapper.has-text,
body.logged-in .chat-input-container .chatgpt-input-wrapper.has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}

/* Fallback for old input wrapper structure */
html body .input-wrapper.has-text,
html body .chat-input-container .input-wrapper.has-text,
.input-wrapper.has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}

/* Emergency fix for any element with has-text class */
html.logged-in .has-text,
body.logged-in .has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}

/* Typing Dots Animation */
.typing-dots span {
    display: inline-block;
    animation: typingDot 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Recording Animation */
.recording-waves span {
    animation: recording 1.5s infinite ease-in-out;
}

.recording-active .recording-waves span {
    animation-play-state: running;
}

.recording-waves span:nth-child(1) {
    animation-delay: 0s;
}

.recording-waves span:nth-child(2) {
    animation-delay: 0.2s;
    animation-duration: 1.8s;
}

.recording-waves span:nth-child(3) {
    animation-delay: 0.4s;
    animation-duration: 1.6s;
}

.recording-waves span:nth-child(4) {
    animation-delay: 0.6s;
    animation-duration: 1.4s;
}

.recording-waves span:nth-child(5) {
    animation-delay: 0.8s;
    animation-duration: 1.2s;
}

/* Loading Animation */
.loading-icon {
    animation: rotate 1s linear infinite;
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Document Animation */
.input-document {
    animation: fadeIn 0.3s ease;
}

/* Welcome Message Animation */
.greeting-message-container .welcome-message::before,
#greetingMessageContainer .welcome-message::before {
    animation: subtleFloat 8s ease-in-out infinite;
}

/* Button Animations */
.message-feedback .copy-btn:active {
    animation: quick-highlight 0.5s ease;
}

.message-feedback .thumbs-up.active,
.message-feedback .thumbs-down.active {
    animation: bounce 0.6s ease;
}

.message-feedback .audio-btn.active {
    animation: pulse 1s infinite ease-in-out;
}

/* Modal Animation */
.modal-content {
    animation: fadeIn 0.3s ease;
}

.premium-modal {
    animation: fadeIn 0.2s;
}

/* Toast Animation */
.simple-toast {
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: translateX(-50%) translateY(-100px);
}

.simple-toast.show {
    transform: translateX(-50%) translateY(0);
}

/* Dropdown Animation */
.chat-menu-dropdown {
    opacity: 0;
    transform: translateY(-5px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.chat-menu-dropdown.active {
    opacity: 1;
    transform: translateY(0);
}

/* Sidebar Animation */
.sidebar {
    transition: width 0.3s ease, transform 0.3s ease;
}

/* Source Panel Animation */
.source-panel {
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.source-panel.active {
    transform: translateX(0);
}

/* Responsive Animation Adjustments */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .chatgpt-input-wrapper.has-text::before {
        border-radius: 29px !important;
    }
}

@media (max-width: 480px) {
    .chatgpt-input-wrapper.has-text::before {
        border-radius: 27px !important;
    }
}
