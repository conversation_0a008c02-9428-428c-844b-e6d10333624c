@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Variables */
@layer base {
  :root {
    /* ShadCN UI Design Tokens */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Enhanced ZiaHR Design Tokens */
    --ziahr-primary: 215 25% 27%;
    --ziahr-primary-foreground: 210 40% 98%;
    --ziahr-secondary: 210 40% 96%;
    --ziahr-secondary-foreground: 215 25% 27%;
    --ziahr-accent: 215 20% 65%;
    --ziahr-accent-foreground: 215 25% 27%;
    --ziahr-muted: 210 40% 96%;
    --ziahr-muted-foreground: 215 16% 47%;

    /* Chat-specific variables */
    --chat-bubble-user: 215 25% 27%;
    --chat-bubble-user-foreground: 210 40% 98%;
    --chat-bubble-assistant: 0 0% 100%;
    --chat-bubble-assistant-foreground: 215 25% 27%;
    --chat-input-bg: 0 0% 100%;
    --chat-input-border: 214.3 31.8% 91.4%;

    /* Layout variables */
    --header-height: 60px;
    --footer-height: 100px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 60px;

    /* Animation variables */
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 250ms;
    --animation-duration-slow: 350ms;

    /* New variables for light mode */
    --bg-primary: #fff;
    --bg-secondary: #f8f9fa;
    --text-primary: #1a202c;
    --text-secondary: #6b7280;
    --accent-color: #2563eb;
    --cta-color: #2563eb;
    --cta-hover: #1d4ed8;
    --border-color: #e5e7eb;
    --card-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  }

  .dark, .theme-dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* Enhanced ZiaHR Dark Tokens */
    --ziahr-primary: 217.2 91.2% 59.8%;
    --ziahr-primary-foreground: 222.2 84% 4.9%;
    --ziahr-secondary: 217.2 32.6% 17.5%;
    --ziahr-secondary-foreground: 210 40% 98%;
    --ziahr-accent: 215 20% 35%;
    --ziahr-accent-foreground: 210 40% 98%;
    --ziahr-muted: 217.2 32.6% 17.5%;
    --ziahr-muted-foreground: 215 20.2% 65.1%;

    /* Chat-specific dark variables */
    --chat-bubble-user: 217.2 91.2% 59.8%;
    --chat-bubble-user-foreground: 222.2 84% 4.9%;
    --chat-bubble-assistant: 217.2 32.6% 17.5%;
    --chat-bubble-assistant-foreground: 210 40% 98%;
    --chat-input-bg: 217.2 32.6% 17.5%;
    --chat-input-border: 217.2 32.6% 17.5%;

    /* New variables for dark mode */
    --bg-primary: #18181b;
    --bg-secondary: #23272f;
    --text-primary: #f3f4f6;
    --text-secondary: #a1a1aa;
    --accent-color: #60a5fa;
    --cta-color: #2563eb;
    --cta-hover: #1d4ed8;
    --border-color: #27272a;
    --card-shadow: 0 2px 8px 0 rgba(0,0,0,0.24);
  }
}

/* Base Styles */
@layer base {
  * {
    @apply border-border;
  }

  html, body {
    font-size: 16px;
    @apply text-base;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  #root {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
  }

  /* Improved scrollbar styling */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

/* Component Styles */
@layer components {
  /* Modern Chat Bubble Styles */
  .chat-bubble {
    @apply relative max-w-[85%] rounded-2xl px-4 py-3 shadow-soft;
    word-wrap: break-word;
    hyphens: auto;
  }

  .chat-bubble-user {
    @apply bg-primary text-primary-foreground ml-auto;
    border-bottom-right-radius: 0.5rem;
  }

  .chat-bubble-assistant {
    @apply bg-card text-card-foreground border;
    border-bottom-left-radius: 0.5rem;
  }

  /* Enhanced Input Styles */
  .chat-input {
    @apply w-full resize-none border-0 bg-transparent px-4 py-3 text-sm placeholder:text-muted-foreground focus:outline-none;
    min-height: 44px;
    max-height: 200px;
  }

  .chat-input-container {
    @apply relative flex items-end gap-2 rounded-2xl border bg-background p-2 shadow-medium;
    transition: all var(--animation-duration-normal) ease-in-out;
  }

  .chat-input-container:focus-within {
    @apply ring-2 ring-ring ring-offset-2;
  }

  /* Modern Button Styles */
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-xl bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-soft transition-all duration-200 hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-xl bg-secondary px-4 py-2 text-sm font-medium text-secondary-foreground shadow-soft transition-all duration-200 hover:bg-secondary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-icon {
    @apply inline-flex h-10 w-10 items-center justify-center rounded-xl transition-all duration-200 hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  /* Suggestion Chip Styles */
  .suggestion-chip {
    @apply inline-flex items-center gap-2 rounded-full bg-secondary px-4 py-2 text-sm font-medium text-secondary-foreground transition-all duration-200 hover:bg-primary hover:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  /* Loading Animation */
  .loading-dots {
    @apply flex items-center gap-1;
  }

  .loading-dots::after {
    content: '';
    @apply h-1 w-1 rounded-full bg-current animate-pulse;
  }

  /* Typing Indicator */
  .typing-indicator {
    @apply flex items-center gap-1 px-4 py-3;
  }

  .typing-dot {
    @apply h-2 w-2 rounded-full bg-muted-foreground animate-bounce;
    animation-delay: calc(var(--i) * 0.1s);
  }

  /* Modern Card Styles */
  .card-modern {
    @apply rounded-2xl border bg-card p-6 shadow-soft;
  }

  /* Sidebar Styles */
  .sidebar-item {
    @apply flex items-center gap-3 rounded-xl px-3 py-2 text-sm font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground;
  }

  .sidebar-item.active {
    @apply bg-primary text-primary-foreground;
  }

  /* Modal Backdrop */
  .modal-backdrop {
    @apply fixed inset-0 z-50 bg-background/80 backdrop-blur-sm;
  }

  /* Smooth Transitions */
  .transition-smooth {
    transition: all var(--animation-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Glass Effect */
  .glass-effect {
    @apply bg-background/80 backdrop-blur-md border border-border/50;
  }
}

/* Dark Theme */
.theme-dark {
  --bg-primary: #1E1E1E;
  --bg-secondary: #2B2B2B;
  --text-primary: #FFFFFF;
  --text-secondary: #EDEDED;
  --accent-color: #999999;
  --accent-color-rgb: 153, 153, 153;
  --accent-hover: #AAAAAA;
  --border-color: #333333;
  --message-user-bg: rgba(255, 255, 255, 0.1);
  --message-bot-bg: rgba(255, 255, 255, 0.05);
  --message-text: #FFFFFF;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --sidebar-bg: #2c2c2c;
  --sidebar-text: #e5e5e5;
  --sidebar-hover: #3a3a3a;
  --sidebar-border: #404040;
  --sidebar-item-hover: rgba(255, 255, 255, 0.1);
  --sidebar-item-active: #404040;
  --sidebar-accent: #e5e5e5;
  --input-bg: #343541;
  --input-border: #444654;
  --input-text: #FFFFFF;
  --input-placeholder: #BBBBBB;
  --welcome-bg: #1E1E1E;
  --chip-bg: rgba(255, 255, 255, 0.1);
  --chip-text: #FFFFFF;
  --chip-hover-bg: #666666;
  --chip-hover-text: #FFFFFF;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  --cta-color: #999999;
  --cta-hover: #AAAAAA;
  --success-color: #AAAAAA;
  --error-color: #777777;
}

/* Global styles and CSS custom properties */
@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  }

  html {
    height: 100%;
  }

  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.2s ease, color 0.2s ease;
    height: 100vh;
    overflow: hidden;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Typography styles */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
  }

  p, li, span, div {
    font-weight: 400;
  }

  /* Remove any unwanted red outlines or borders */
  .chat-messages *,
  .welcome-container *,
  .chat-input-container * {
    outline: none !important;
    border-color: var(--border-color) !important;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-border;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-accent;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-accent;
  }

  /* Dark theme specific overrides */
  .theme-dark .chatgpt-input-wrapper {
    background: #343541 !important;
    border-color: #444654 !important;
  }

  .theme-dark .chatgpt-input-area textarea {
    color: #FFFFFF !important;
  }

  .theme-dark .chatgpt-input-area textarea::placeholder {
    color: #BBBBBB !important;
  }

  .theme-dark .chatgpt-tool-btn {
    color: #BBBBBB !important;
  }

  .theme-dark .chatgpt-tool-btn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #FFFFFF !important;
  }

  .theme-dark .chatgpt-tool-btn:active, .theme-dark .chatgpt-tool-btn.selected {
    background: rgba(255, 255, 255, 0.2) !important;
  }

  .theme-dark .suggestion-chip {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #FFFFFF !important;
    border-color: #444654 !important;
  }

  .theme-dark .suggestion-chip:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-color: #666666 !important;
  }

  .theme-dark .suggestion-chip:active {
    background-color: rgba(255, 255, 255, 0.3) !important;
  }

  /* Focus states for accessibility */
  .chatgpt-input-wrapper:focus-within {
    border-color: #9ca3af !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .theme-dark .chatgpt-input-wrapper:focus-within {
    border-color: #666666 !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3) !important;
  }

  .chatgpt-input-wrapper.has-text:focus-within {
    border-color: transparent !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  }

  .theme-dark .chatgpt-input-wrapper.has-text:focus-within {
    border-color: transparent !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  }
}

@layer components {
  /* Pre-login UI Components - Exact match to Flask app */
  .pre-login-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  .pre-login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
  }

  .pre-login-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
  }

  .pre-login-logo i {
    font-size: 24px;
    color: var(--accent-color);
  }

  .login-btn {
    padding: 8px 16px;
    background-color: var(--cta-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .login-btn:hover {
    background-color: var(--cta-hover);
  }

  .pre-login-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 32px 16px;
    position: relative;
  }

  .pre-login-welcome-container {
    text-align: center;
    max-width: 800px;
    margin-bottom: 48px;
  }

  .pre-login-welcome-message h2 {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
  }

  .pre-login-welcome-message p {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
  }

  .pre-login-bottom-input-container {
    position: absolute;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 560px;
    min-width: 340px;
    padding: 4px 24px;
  }

  /* ChatGPT Input Wrapper - Exact match to Flask app */
  .chatgpt-input-wrapper {
    display: flex !important;
    flex-direction: column !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 26px !important;
    padding: 16px !important;
    gap: 12px !important;
    min-height: 80px !important;
    width: 100% !important;
    position: relative !important;
    overflow: visible !important;
  }

  .chatgpt-input-wrapper.has-text:focus-within {
    border-color: transparent !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  }

  .chatgpt-input-wrapper:focus-within {
    border-color: #9ca3af !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .chatgpt-input-area {
    width: 116% !important;
    order: 1 !important;
  }

  .chatgpt-input-area textarea {
    width: 116% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    resize: none !important;
    font-size: 16px !important;
    line-height: 24px !important;
    color: #374151 !important;
    font-family: inherit !important;
    padding: 0 !important;
    margin: 0 !important;
    min-height: 24px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  .chatgpt-input-area textarea::-webkit-scrollbar {
    display: none !important;
  }

  .chatgpt-input-area textarea::placeholder {
    color: #9ca3af !important;
    font-size: 16px !important;
  }

  .pre-login-input {
    width: 100% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    resize: none !important;
    font-size: 16px !important;
    line-height: 24px !important;
    color: #374151 !important;
    font-family: inherit !important;
    padding: 0 !important;
    margin: 0 !important;
    min-height: 24px !important;
    max-height: 200px !important;
  }

  .pre-login-input::placeholder {
    color: #9ca3af !important;
    font-size: 16px !important;
  }

  .chatgpt-tools-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    order: 2 !important;
    width: 100% !important;
  }

  .chatgpt-left-tools,
  .chatgpt-right-tools {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  .chatgpt-tool-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    background: transparent !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    color: #6b7280 !important;
  }

  .chatgpt-tool-btn:hover {
    background: #f7f7f8 !important;
    box-shadow: none !important;
  }

  .chatgpt-tool-btn:active, .chatgpt-tool-btn.selected {
    background: #f3f4f6 !important;
    box-shadow: none !important;
  }

  .chatgpt-tool-btn i {
    margin: auto !important;
    display: block !important;
    font-size: 14px !important;
  }

  .chatgpt-send-btn {
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    border-radius: 8px !important;
    background: #2563eb !important;
    color: white !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
  }

  .chatgpt-send-btn:hover:not(:disabled) {
    background: #1a1a1a !important;
  }

  .chatgpt-send-btn:disabled {
    background: #d1d5db !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
  }

  .chatgpt-send-btn svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* Suggestion Chips - Exact match to Flask app */
  .suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 12px !important;
    justify-content: center;
    margin-top: 16px !important;
  }

  .suggestion-chip {
    background-color: var(--bg-primary, #FFFFFF);
    color: var(--text-primary, #000000);
    border: 1px solid var(--border-color, #E5E5E5);
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .suggestion-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
  }

  .suggestion-chip:hover {
    background-color: rgba(0, 0, 0, 0.08) !important;
    border-color: var(--text-secondary, #666666) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .suggestion-chip:hover::before {
    left: 100%;
  }

  .suggestion-chip:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
    background-color: rgba(0, 0, 0, 0.12) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  .suggestion-chip i, .suggestion-chip svg, .suggestion-chip img {
    width: 18px !important;
    height: 18px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 8px !important;
  }

  /* App Container - Exact match to Flask app */
  .app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    max-width: 100vw;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
  }

  /* Sidebar - Exact match to Flask app */
  .sidebar {
    width: 260px;
    background-color: var(--sidebar-bg, #f8f9fa);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
    overflow: hidden;
  }

  .sidebar.collapsed {
    width: 60px;
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    gap: 8px;
    padding: 4px 8px;
    min-height: 32px;
  }

  .sidebar-brand-logo {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0 4px;
  }

  .sidebar.collapsed .sidebar-brand-logo {
    justify-content: center;
  }

  .sidebar-app-icon {
    width: 24px;
    height: 24px;
  }

  .sidebar-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: var(--text-secondary);
  }

  .sidebar-toggle:hover {
    background-color: var(--bg-secondary);
  }

  .sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }

  .sidebar-menu {
    padding: 8px 0;
  }

  .sidebar-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: var(--text-secondary);
  }

  .sidebar-menu-item:hover {
    background-color: var(--bg-secondary);
  }

  .sidebar-menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-menu-text {
    font-size: 14px;
    font-weight: 500;
  }

  .sidebar.collapsed .sidebar-menu-text {
    display: none;
  }

  .sidebar.collapsed .sidebar-menu-item {
    justify-content: center;
    padding: 8px;
  }

  .sidebar.collapsed .sidebar-menu-icon {
    margin-right: 0;
  }

  .sidebar-conversations {
    flex: 1;
    overflow-y: auto;
  }

  .sidebar-section-header {
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
  }

  .sidebar-section-title {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-secondary);
  }

  .chat-history-list {
    padding: 0;
    margin: 0;
  }

  .chat-history-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: var(--text-secondary);
    font-size: 13px;
  }

  .chat-history-item:hover {
    background-color: var(--bg-secondary);
  }

  .chat-history-item.active {
    background-color: var(--accent-color);
    color: white;
  }

  .chat-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .chat-date {
    font-size: 11px;
    opacity: 0.7;
  }

  .chat-actions {
    display: none;
    gap: 4px;
  }

  .chat-history-item:hover .chat-actions {
    display: flex;
  }

  .chat-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    color: var(--text-secondary);
  }

  .chat-actions button:hover {
    background-color: var(--bg-secondary);
  }

  .sidebar-bottom {
    padding: 16px;
    border-top: 1px solid var(--border-color);
  }

  .upload-form {
    margin-bottom: 8px;
  }

  .upload-status {
    font-size: 12px;
    color: var(--text-secondary);
  }

  .sidebar.collapsed .sidebar-conversations,
  .sidebar.collapsed .sidebar-bottom {
    display: none;
  }

  /* Header - Exact match to Flask app */
  .chat-header {
    height: var(--header-height);
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    z-index: 100;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 24px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-new-chat-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .header-new-chat-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .header-brand h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }

  .user-account-dropdown {
    position: relative;
  }

  .header-action-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    font-size: 20px;
    transition: all 0.2s ease;
  }

  .header-action-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    width: 280px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    z-index: 1000;
    animation: slideIn 0.2s ease-out;
  }

  .user-info {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
  }

  .user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
  }

  .user-email {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 2px;
  }

  .user-id {
    font-size: 12px;
    color: var(--text-secondary);
  }

  .dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
  }

  .dropdown-menu-items {
    padding: 8px 0;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    background: transparent;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: left;
  }

  .dropdown-item:hover {
    background-color: var(--bg-secondary);
  }

  .dropdown-item i {
    margin-right: 12px;
    width: 16px;
    color: var(--text-secondary);
  }

  .dropdown-item span {
    font-size: 14px;
  }

  /* Main Content - Exact match to Flask app */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .chat-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }

  .welcome-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
  }

  .welcome-message h2 {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
  }

  .welcome-message p {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
  }

  .chat-messages {
    max-width: 800px;
    margin: 0 auto;
  }

  /* Greeting Message Container - Exact match to Flask app */
  .greeting-message-container {
    position: static !important;
    margin-bottom: 8px !important;
    margin-top: 0 !important;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }

  #greetingMessageContainer {
    display: block !important;
    text-align: center;
    margin: 0 auto;
  }

  .chat-messages.has-messages #greetingMessageContainer {
    display: none !important;
  }

  .greeting-message {
    color: var(--text-primary, #000000) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    margin: 0 0 16px 0 !important;
    text-align: center !important;
  }

  .greeting-message-container .welcome-message h2 {
    margin-bottom: 16px !important;
    color: var(--text-primary, #000000) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
  }

  .greeting-message-container .welcome-message p {
    margin-bottom: 24px !important;
    color: var(--text-secondary, #666666) !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
  }

  /* Chat Input Container - Exact match to Flask app */
  .chat-input-container {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;
    max-width: 100vw;
    z-index: 1000;
    padding: 4px 24px !important;
    max-width: 560px !important;
    min-width: 340px !important;
    flex-shrink: 0;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .chat-input-form {
    width: 100% !important;
    max-width: 648px !important;
    min-width: 0;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Bottom Tools Section - Exact match to Flask app */
  .chatgpt-bottom-tools {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    order: 2 !important;
    width: 100% !important;
  }

  /* Message Styles - Exact match to Flask app */
  .message-container {
    margin-bottom: 24px;
  }

  .message-content {
    line-height: 1.6;
    margin-bottom: 8px;
  }

  .bot-message .message-content {
    color: var(--text-primary);
  }

  .user-message .message-content {
    color: var(--text-primary);
  }

  .message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }

  .bot-message .message-footer {
    justify-content: flex-start;
  }

  .user-message .message-footer {
    justify-content: flex-end;
  }

  .message-time {
    font-size: 12px;
    color: var(--text-secondary);
    opacity: 0.8;
  }

  .message-feedback {
    display: flex;
    gap: 4px;
  }

  .message-feedback .feedback-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
  }

  .message-feedback .feedback-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .message-feedback .feedback-btn:active {
    transform: scale(0.95);
  }

  .message-feedback .feedback-btn i {
    font-size: 12px;
  }

  .message-bubble {
    padding: 16px;
    border-radius: 8px;
    max-width: 80%;
    word-wrap: break-word;
  }

  .message-user {
    background-color: var(--message-user-bg);
    color: var(--message-text);
    margin-left: auto;
  }

  .message-bot {
    background-color: var(--message-bot-bg);
    color: var(--message-text);
    border: 1px solid var(--border-color);
  }

  .message-timestamp {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
  }

  .message-files {
    margin-top: 8px;
  }

  .message-file {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    margin-bottom: 4px;
  }

  .message-file i {
    margin-right: 8px;
    color: var(--text-secondary);
  }

  .message-file-name {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
  }

  .message-file-size {
    font-size: 12px;
    color: var(--text-secondary);
  }

  /* Typing Indicator */
  .typing-indicator {
    display: flex;
    align-items: center;
    padding: 16px;
    color: var(--text-secondary);
  }

  .typing-dots {
    display: flex;
    gap: 4px;
  }

  .typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    animation: typingBounce 1.4s infinite ease-in-out;
  }

  .typing-dot:nth-child(1) { animation-delay: -0.32s; }
  .typing-dot:nth-child(2) { animation-delay: -0.16s; }
  .typing-dot:nth-child(3) { animation-delay: 0s; }

  @keyframes typingBounce {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Button Styles */
  .btn-primary {
    background-color: var(--cta-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .btn-primary:hover {
    background-color: var(--cta-hover);
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
  }

  .btn-secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-secondary:hover {
    background-color: var(--border-color);
  }

  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
  }

  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .icon-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
  }

  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .modal-content {
    background-color: var(--bg-primary);
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  /* Form Styles */
  .form-group {
    margin-bottom: 16px;
  }

  .form-label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--text-primary);
  }

  .form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--input-text);
    font-size: 14px;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.1);
  }

  .form-input::placeholder {
    color: var(--input-placeholder);
  }

  /* Loading Spinner */
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Input Suggestions - Exact match to Flask app */
  .input-suggestions {
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 12px;
  }

  .input-suggestions .suggestion-chip {
    background-color: var(--bg-primary, #FFFFFF);
    color: var(--text-primary, #000000);
    border: 1px solid var(--border-color, #E5E5E5);
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .input-suggestions .suggestion-chip:hover {
    background-color: rgba(0, 0, 0, 0.08) !important;
    border-color: var(--text-secondary, #666666) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .input-suggestions .suggestion-chip:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
    background-color: rgba(0, 0, 0, 0.12) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* File Display Area - Exact match to Flask app */
  .file-list-container {
    display: none;
  }

  .input-attachments-container {
    display: none;
  }

  .file-display-area {
    margin-top: 8px;
  }

  /* Placeholder styles for textarea */
  textarea::placeholder, .chatgpt-input-area textarea::placeholder {
    color: #9ca3af !important;
    font-size: 16px !important;
  }

  .pre-login-input::placeholder {
    color: #9ca3af !important;
    font-size: 16px !important;
  }

  /* Utility Classes */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Animation utility classes */
  .animate-in {
    animation: fadeInUp 0.3s ease-out;
  }

  .fade-in-0 {
    animation: fadeIn 0.2s ease-out;
  }

  .zoom-in-95 {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes pulseRecording {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .pre-login-welcome-message h2 {
      font-size: 24px;
    }

    .pre-login-welcome-message p {
      font-size: 16px;
    }

    .suggestion-chips {
      flex-direction: column;
      align-items: center;
    }

    .pre-login-bottom-input-container {
      padding: 4px 16px;
    }

    .app-container {
      flex-direction: column;
      width: 100vw;
      max-width: 100vw;
    }

    .sidebar {
      width: 100%;
      height: auto;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
    }

    .sidebar.collapsed {
      width: 100%;
    }

    .sidebar-conversations {
      max-height: 200px;
    }

    .sidebar.collapsed .sidebar-conversations {
      display: none;
    }

    .main-content {
      flex: 1;
      min-width: 0;
    }

    .header-content {
      padding: 8px 16px;
    }

    .chat-input-container {
      padding: 4px 16px;
    }

    .welcome-message h2 {
      font-size: 24px;
    }

    .welcome-message p {
      font-size: 16px;
    }

    .message-bubble {
      max-width: 90%;
    }

    .user-dropdown-menu {
      right: 16px;
      left: 16px;
    }

    .chatgpt-input-wrapper {
      min-height: 60px !important;
    }

    .chatgpt-input-area {
      width: 100% !important;
    }

    .chatgpt-input-area textarea {
      width: 100% !important;
    }
  }

  @media (max-width: 480px) {
    .header-content {
      padding: 8px 12px;
    }

    .chat-input-container {
      padding: 4px 12px;
    }

    .pre-login-header {
      padding: 12px 16px;
    }

    .suggestion-chip {
      padding: 6px 12px;
      font-size: 12px;
    }

    .message-bubble {
      max-width: 95%;
      padding: 12px;
    }

    .welcome-message h2 {
      font-size: 20px;
    }

    .user-dropdown-menu {
      right: 8px;
      left: 8px;
    }
  }

  /* Visibility Classes */
  .not-logged-in .logged-in-only {
    display: none !important;
  }

  .logged-in .not-logged-in-only {
    display: none !important;
  }

  /* Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
  }
}
