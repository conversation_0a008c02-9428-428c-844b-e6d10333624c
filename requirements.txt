# ───────────── Web Frameworks ─────────────
flask==3.0.0
flask-cors==4.0.0
python-dotenv==1.0.0
chardet==5.2.0
uvicorn==0.35.0
fastapi==0.115.14
starlette==0.46.2
aiohttp==3.12.14

# ───────────── Langchain & Integrations ─────────────
langchain==0.3.25
langchain-core==0.3.65
langchain-community==0.3.25
langsmith==0.3.45
langchain-groq==0.3.2

# ───────────── NLP & Embeddings ─────────────
sentence-transformers==4.1.0
huggingface-hub==0.33.0
torch==2.3.0
transformers==4.49.0
rapidfuzz==3.13.0
spacy-transformers==1.3.9
spacy==3.8.7
SpeechRecognition==3.14.3
tiktoken==0.9.0
PyJWT==2.10.1

# ───────────── Vector Databases ─────────────
qdrant-client==1.14.3

# ───────────── Core Libraries ─────────────
numpy==1.26.2
pandas==2.3.0
scikit-learn==1.7.0
joblib==1.5.1

# ───────────── File Parsing ─────────────
python-magic==0.4.27
PyPDF2==3.0.1
python-docx==1.0.1
openpyxl==3.1.2
pymupdf==1.26.1
python-pptx==1.0.2
extract-msg==0.54.1
beautifulsoup4==4.13.4
pytesseract==0.3.13
pillow==11.2.1
psutil==7.0.0

# ───────────── Security & Utilities ─────────────
bcrypt==4.0.1
pyotp==2.9.0
qrcode[pil]==8.2
sounddevice==0.5.2
passlib==1.7.4
langdetect==1.0.9
concurrent-log-handler==0.9.28

# ───────────── General Utilities ─────────────
markdown==3.5
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0
tqdm==4.66.1

# ───────────── Testing ─────────────
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio==0.23.8

# ───────────── Dev & Linting ─────────────
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.0
