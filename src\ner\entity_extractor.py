import spacy
from spacy.tokens import DocBin
from spacy.training import Example
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
import json
import time
import re
import os
import numpy as np
from contextlib import contextmanager
import threading
from functools import lru_cache
import warnings
import random  # Added missing import

try:
    from ..config import DATA_DIR
    from ..utils.logger import get_logger
except ImportError:
    # Fallback for standalone execution/testing
    print("Warning: Running without full project structure. DATA_DIR and get_logger are mocked.")
    BASE_DIR = Path(__file__).resolve().parent.parent
    DATA_DIR = BASE_DIR / "data"
    if not DATA_DIR.exists():
        DATA_DIR.mkdir(parents=True)
    class MockLogger:
        def __init__(self, name): self.name = name
        def info(self, msg): print(f"INFO: {self.name} - {msg}")
        def warning(self, msg): print(f"WARNING: {self.name} - {msg}")
        def error(self, msg, exc_info=False): print(f"ERROR: {self.name} - {msg}")
    def get_logger(name): return MockLogger(name)

logger = get_logger(__name__)

# --- Configuration and Constants ---

TRAINING_DATA_PATH = DATA_DIR / "training" / "entity_training_data.jsonl"
MODEL_DIR = DATA_DIR / "models" / "entity_extractor"
CALIBRATION_PATH = DATA_DIR / "calibration" / "entity_calibration_stats.json"

# Ensure directories exist
for path in [MODEL_DIR, DATA_DIR / "config", DATA_DIR / "training", DATA_DIR / "calibration"]:
    path.mkdir(parents=True, exist_ok=True)

# --- Custom Exceptions ---
class EntityExtractionError(Exception):
    """Base exception for entity extraction errors."""
    pass

class ModelLoadError(EntityExtractionError):
    """Raised when the spaCy model fails to load."""
    pass

class InvalidInputError(EntityExtractionError):
    """Raised when input text is invalid or exceeds limits."""
    pass

class ModelUpdateError(EntityExtractionError):
    """Raised when model update/training fails."""
    pass

class PatternManagementError(EntityExtractionError):
    """Raised for errors during pattern export/load."""
    pass

class EntityExtractor:
    """
    Production-grade Named Entity Recognition (NER) and Span Categorization for HR queries.
    
    Features:
    - Thread-safe operations
    - Proper error handling and logging
    - Performance monitoring
    - Batch processing optimization
    - Confidence calibration
    - Pattern management
    - Input validation and sanitization
    """
    
    def __init__(self, model_path: Path = MODEL_DIR, enable_gpu: bool = False, max_workers: int = 4):
        """
        Initialize the EntityExtractor with production-grade configurations.
        
        Args:
            model_path (Path): Path to the trained spaCy model
            enable_gpu (bool): Whether to use GPU acceleration if available
            max_workers (int): Maximum number of worker threads for batch processing
        """
        self.model_path = model_path
        self.enable_gpu = enable_gpu
        self.max_workers = max_workers
        self.nlp: Optional[spacy.Language] = None
        self.patterns: Dict[str, List[str]] = {}
        self.calibration_stats: Dict[str, Any] = {}
        self._lock = threading.RLock()
        self._performance_stats = {
            'total_requests': 0,
            'total_processing_time': 0.0,
            'error_count': 0
        }
        
        self.load_model()
        self.load_calibration_stats()
        logger.info(f"EntityExtractor initialized with model from {self.model_path}")

    def load_model(self):
        """Load the spaCy model with proper error handling and GPU support."""
        with self._lock:
            if self.enable_gpu:
                try:
                    spacy.require_gpu()
                    logger.info("GPU support enabled for spaCy")
                except Exception as e:
                    logger.warning(f"Failed to enable GPU: {e}. Falling back to CPU.")
            
            # Check if trained model exists
            if (self.model_path / "meta.json").exists():
                try:
                    self.nlp = spacy.load(self.model_path)
                    logger.info(f"Trained spaCy model loaded successfully from: {self.model_path}")
                except Exception as e:
                    logger.error(f"Failed to load trained model from {self.model_path}: {e}")
                    logger.info("Falling back to blank model creation...")
                    self._create_blank_model()
            else:
                logger.info(f"No trained model found at {self.model_path}. Creating blank model...")
                self._create_blank_model()

            # Optimize model for inference
            self._optimize_model()
            
            # Add entity ruler patterns
            self.add_entity_ruler_patterns()

    def _create_blank_model(self):
        """Create a blank spaCy model with NER component."""
        try:
            self.nlp = spacy.blank("en")
            ner = self.nlp.add_pipe("ner")
            
            # Add comprehensive HR labels
            hr_labels = [
                "DOCUMENT_TYPE", "HR_PROCESS", "BENEFIT_TYPE", "LEAVE_TYPE", 
                "COMPENSATION_TYPE", "TAX_COMPONENT", "ID_TYPE", "POLICY_NAME", 
                "DEPARTMENT", "JOB_ROLE", "EMPLOYMENT_TYPE", "SALARY_RANGE", 
                "FINANCIAL_YEAR", "CITY", "HOLIDAY_NAME", "EMPLOYEE_ID",
                "PERSON", "ORG", "GPE", "MONEY", "TIME", "ADDRESS", "HR_EVENT",
                "POLICY_TYPE"  # Added missing label
            ]
            
            for label in hr_labels:
                if label not in ner.labels:
                    ner.add_label(label)
            
            logger.info("Blank spaCy model created with NER component and HR labels.")
            
        except Exception as e:
            logger.error(f"Failed to create blank model: {e}")
            raise ModelLoadError(f"Failed to create blank model: {e}")

    def _optimize_model(self):
        """Optimize model for faster inference."""
        if self.nlp:
            # Disable unnecessary components for inference
            unnecessary_pipes = ['tagger', 'parser', 'lemmatizer']
            for pipe in unnecessary_pipes:
                if pipe in self.nlp.pipe_names:
                    self.nlp.disable_pipe(pipe)
                    logger.info(f"Disabled '{pipe}' pipe for inference optimization.")

    @contextmanager
    def _performance_monitor(self):
        """Context manager for monitoring performance metrics."""
        start_time = time.time()
        try:
            with self._lock:
                self._performance_stats['total_requests'] += 1
            yield
        except Exception as e:
            with self._lock:
                self._performance_stats['error_count'] += 1
            raise
        finally:
            processing_time = time.time() - start_time
            with self._lock:
                self._performance_stats['total_processing_time'] += processing_time

    def extract_entities(self, texts: Union[str, List[str]], 
                         max_length: int = 1000,
                         batch_size: int = 32) -> List[Dict[str, Any]]:
        """
        Extract entities from text(s) with production-grade features.
        
        Args:
            texts: Input text or list of texts
            max_length: Maximum allowed length per text
            batch_size: Batch size for processing multiple texts
            
        Returns:
            List of dictionaries containing extraction results
        """
        with self._performance_monitor():
            if self.nlp is None:
                raise ModelLoadError("Model is not loaded.")

            # Input validation and normalization
            texts = self._validate_and_normalize_input(texts, max_length)
            
            results = []
            
            # Process in batches for efficiency
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_results = self._process_batch(batch)
                results.extend(batch_results)
            
            return results

    def _validate_and_normalize_input(self, texts: Union[str, List[str]], 
                                      max_length: int) -> List[str]:
        """Validate and normalize input texts."""
        if isinstance(texts, str):
            texts = [texts]
        
        if not isinstance(texts, list) or not all(isinstance(t, str) for t in texts):
            raise InvalidInputError("Input must be string or list of strings")
        
        normalized_texts = []
        for i, text in enumerate(texts):
            if not text or not text.strip():
                logger.warning(f"Empty text at index {i}, skipping")
                normalized_texts.append("")
                continue
            
            if len(text) > max_length:
                logger.warning(f"Text at index {i} truncated from {len(text)} to {max_length} chars")
                text = text[:max_length]
            
            # Basic text sanitization
            text = re.sub(r'\x00', '', text)
            text = text.strip()
            normalized_texts.append(text)
        
        return normalized_texts

    def _process_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Process a batch of texts efficiently."""
        results = []
        
        try:
            # Ensure NER is available
            if 'ner' not in self.nlp.pipe_names:
                logger.error("NER component not found in spaCy pipeline.")
                raise ModelLoadError("NER component missing in model.")

            # Process with spaCy pipe for efficiency
            docs = list(self.nlp.pipe(texts))
            
            for text, doc in zip(texts, docs):
                if not text.strip():
                    results.append({
                        "text": text,
                        "entities": [],
                        "confidence_score": 0.0,
                        "processing_time": 0.0
                    })
                    continue
                
                start_time = time.time()
                entities = self._extract_entities_from_doc(doc)
                processing_time = time.time() - start_time
                
                overall_confidence = self._calculate_overall_confidence(entities)
                
                results.append({
                    "text": text,
                    "entities": entities,
                    "confidence_score": float(overall_confidence),
                    "processing_time": processing_time
                })
                
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            raise EntityExtractionError(f"Batch processing failed: {e}")
        
        return results

    def _extract_entities_from_doc(self, doc: spacy.tokens.Doc) -> List[Dict[str, Any]]:
        """Extract entities from a spaCy Doc object."""
        entities = []
        
        for ent in doc.ents:
            confidence = self._get_calibrated_confidence(ent)
            
            entities.append({
                "start": ent.start_char,
                "end": ent.end_char,
                "label": ent.label_,
                "text": ent.text,
                "score": float(confidence)
            })
        
        return entities

    def _get_calibrated_confidence(self, entity: spacy.tokens.Span) -> float:
        """Get calibrated confidence score for an entity."""
        try:
            # Try to get actual confidence from model if available
            if hasattr(entity, 'score'):
                raw_score = entity.score
            else:
                # Fallback: use heuristic scoring
                raw_score = self._heuristic_confidence(entity)
            
            # Apply calibration if available
            calibrated_score = self._apply_calibration(entity.label_, raw_score)
            
            return min(max(calibrated_score, 0.0), 1.0)
            
        except Exception as e:
            logger.warning(f"Error calculating confidence for entity '{entity.text}': {e}")
            return 0.5

    def _heuristic_confidence(self, entity: spacy.tokens.Span) -> float:
        """Calculate heuristic confidence based on entity characteristics."""
        base_confidence = 0.7
        
        # Length bonus
        length_bonus = min(len(entity.text) * 0.02, 0.2)
        
        # Capitalization bonus
        caps_bonus = 0.1 if entity.text and entity.text[0].isupper() else 0.0
        
        # Label-specific confidence
        label_confidence = {
            "PERSON": 0.85, "ORG": 0.80, "GPE": 0.75,
            "MONEY": 0.90, "DATE": 0.85, "TIME": 0.80,
            "EMPLOYEE_ID": 0.95, "DEPARTMENT": 0.85, 
            "LEAVE_TYPE": 0.80, "POLICY_TYPE": 0.75,
            "DOCUMENT_TYPE": 0.85, "HR_PROCESS": 0.70,
            "BENEFIT_TYPE": 0.80, "COMPENSATION_TYPE": 0.75,
            "TAX_COMPONENT": 0.75, "ID_TYPE": 0.90,
            "POLICY_NAME": 0.75, "JOB_ROLE": 0.70,
            "EMPLOYMENT_TYPE": 0.70, "SALARY_RANGE": 0.80,
            "FINANCIAL_YEAR": 0.90, "CITY": 0.85, 
            "HOLIDAY_NAME": 0.80, "ADDRESS": 0.85, 
            "HR_EVENT": 0.75
        }
        
        label_adjustment = label_confidence.get(entity.label_, 0.0)
        
        return min(base_confidence + length_bonus + caps_bonus + label_adjustment, 1.0)

    def _apply_calibration(self, label: str, raw_score: float) -> float:
        """Apply calibration to raw confidence scores."""
        if label in self.calibration_stats:
            calibration = self.calibration_stats[label]
            slope = calibration.get('slope', 1.0)
            intercept = calibration.get('intercept', 0.0)
            return raw_score * slope + intercept
        
        return raw_score

    def _calculate_overall_confidence(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate overall confidence for the entire extraction."""
        if not entities:
            return 0.0
        
        total_weight = 0
        weighted_sum = 0
        
        for entity in entities:
            weight = len(entity['text'])
            weighted_sum += entity['score'] * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def load_calibration_stats(self):
        """Load calibration statistics for confidence scoring."""
        try:
            if CALIBRATION_PATH.exists():
                with open(CALIBRATION_PATH, 'r') as f:
                    self.calibration_stats = json.load(f)
                logger.info(f"Calibration stats loaded from {CALIBRATION_PATH}")
            else:
                logger.info("No calibration stats found, using default confidence scoring")
                self.calibration_stats = {}
        except Exception as e:
            logger.warning(f"Failed to load calibration stats: {e}")
            self.calibration_stats = {}

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        with self._lock:
            stats = self._performance_stats.copy()
            if stats['total_requests'] > 0:
                stats['avg_processing_time'] = stats['total_processing_time'] / stats['total_requests']
                stats['error_rate'] = stats['error_count'] / stats['total_requests']
            else:
                stats['avg_processing_time'] = 0.0
                stats['error_rate'] = 0.0
            return stats

    def load_patterns_from_json(self, file_path: Path):
        """Load rule-based patterns from JSON file."""
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.patterns = json.load(f)
                logger.info(f"Patterns loaded from {file_path}")
            else:
                logger.warning(f"Pattern file not found: {file_path}")
                self.patterns = {}
        except Exception as e:
            logger.error(f"Failed to load patterns: {e}")
            raise PatternManagementError(f"Pattern loading failed: {e}")

    def export_patterns_to_json(self, file_path: Path):
        """Export patterns to JSON file."""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.patterns, f, ensure_ascii=False, indent=2)
            logger.info(f"Patterns exported to {file_path}")
        except Exception as e:
            logger.error(f"Failed to export patterns: {e}")
            raise PatternManagementError(f"Pattern export failed: {e}")

    def add_entity_ruler_patterns(self):
        """Add entity ruler with patterns for better HR entity recognition."""
        try:
            if self.nlp is None:
                logger.warning("NLP model not loaded, cannot add entity ruler patterns.")
                return

            # Remove existing entity ruler if present
            if "entity_ruler" in self.nlp.pipe_names:
                self.nlp.remove_pipe("entity_ruler")

            # Add entity ruler before NER
            ruler = self.nlp.add_pipe("entity_ruler", before="ner")

            # Define comprehensive HR patterns
            hr_patterns = [
                # Employee ID patterns
                {"label": "EMPLOYEE_ID", "pattern": [{"TEXT": {"REGEX": r"^(EMP|E)\d{3,7}$"}}]},
                
                # Department patterns
                {"label": "DEPARTMENT", "pattern": [{"LOWER": {"IN": ["finance", "hr", "it", "engineering", "marketing", "sales", "operations", "legal", "customer support", "admin", "procurement", "quality assurance", "research and development"]}}]},
                
                # Leave type patterns
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "sick"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "maternity"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "paternity"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "casual"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "earned"}, {"LOWER": "leave"}]},
                
                # Document type patterns
                {"label": "DOCUMENT_TYPE", "pattern": [{"LOWER": {"IN": ["payslip", "pay slip", "offer letter", "experience letter", "relieving letter"]}}]},
                
                # Benefit type patterns
                {"label": "BENEFIT_TYPE", "pattern": [{"LOWER": {"IN": ["health insurance", "dental insurance", "life insurance", "gratuity", "meal allowance"]}}]},
                
                # Financial year pattern
                {"label": "FINANCIAL_YEAR", "pattern": [{"TEXT": {"REGEX": r"FY \d{4}-\d{2}"}}]},
                
                # Salary range pattern
                {"label": "SALARY_RANGE", "pattern": [{"TEXT": {"REGEX": r"\d{1,3}(,\d{3})* - \d{1,3}(,\d{3})*"}}]},
            ]
            
            ruler.add_patterns(hr_patterns)
            logger.info("Entity ruler patterns added successfully")
            
        except Exception as e:
            logger.warning(f"Failed to add entity ruler patterns: {e}")

    def train_model(self, 
                    training_data_path: Path = TRAINING_DATA_PATH, 
                    n_iter: int = 10, 
                    dropout: float = 0.2, 
                    learn_rate: float = 0.001,
                    output_path: Path = MODEL_DIR):
        """
        Train the spaCy NER model using custom dataset.
        
        Args:
            training_data_path (Path): Path to JSONL training data
            n_iter (int): Number of training iterations
            dropout (float): Dropout rate
            learn_rate (float): Learning rate
            output_path (Path): Output directory for trained model
        """
        logger.info(f"Starting model training from data at {training_data_path}")
        
        if not training_data_path.exists():
            logger.error(f"Training data file not found: {training_data_path}")
            raise FileNotFoundError(f"Training data file not found at {training_data_path}")

        # Load training data
        TRAIN_DATA = []
        try:
            with open(training_data_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())
                        
                        # Handle different data formats
                        if 'spans' in data:
                            entities = [(s['start'], s['end'], s['label']) for s in data['spans']]
                        elif 'entities' in data:
                            if isinstance(data['entities'], list) and data['entities']:
                                if isinstance(data['entities'][0], dict):
                                    # Convert dict format to tuple
                                    entities = [(e['start'], e['end'], e['label']) for e in data['entities']]
                                else:
                                    # Already in tuple format
                                    entities = data['entities']
                            else:
                                entities = []
                        else:
                            logger.warning(f"Line {line_num}: No entities or spans found, skipping")
                            continue
                        
                        TRAIN_DATA.append((data['text'], {'entities': entities}))
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"Line {line_num}: Invalid JSON, skipping - {e}")
                        continue
                    except KeyError as e:
                        logger.warning(f"Line {line_num}: Missing required field {e}, skipping")
                        continue
                        
            logger.info(f"Loaded {len(TRAIN_DATA)} training examples")
            
            if not TRAIN_DATA:
                raise ModelUpdateError("No valid training data loaded")
                
        except Exception as e:
            logger.error(f"Error loading training data: {e}")
            raise ModelUpdateError(f"Failed to load training data: {e}")

        # Initialize model for training
        try:
            # Start with blank model for custom training
            nlp = spacy.blank("en")
            ner = nlp.add_pipe("ner")
            
            # Add all labels from training data
            for text, annotations in TRAIN_DATA:
                for start, end, label in annotations.get('entities', []):
                    ner.add_label(label)
            
            logger.info(f"Added {len(ner.labels)} unique labels to NER component")
            
        except Exception as e:
            logger.error(f"Error initializing model for training: {e}")
            raise ModelUpdateError(f"Model initialization failed: {e}")

        # Convert to spaCy Examples
        try:
            examples = []
            for text, annotations in TRAIN_DATA:
                doc = nlp.make_doc(text)
                example = Example.from_dict(doc, annotations)
                examples.append(example)
                
        except Exception as e:
            logger.error(f"Error creating training examples: {e}")
            raise ModelUpdateError(f"Example creation failed: {e}")

        # Initialize training
        optimizer = nlp.initialize()
        
        # Training loop
        logger.info("Starting training...")
        for iteration in range(n_iter):
            random.shuffle(examples)
            losses = {}
            
            # Process in batches
            batches = spacy.util.minibatch(examples, size=8)
            for batch in batches:
                nlp.update(batch, drop=dropout, losses=losses, sgd=optimizer)
            
            logger.info(f"Iteration {iteration + 1}/{n_iter} - Losses: {losses}")

        # Save trained model
        try:
            output_path.mkdir(parents=True, exist_ok=True)
            nlp.to_disk(output_path)
            logger.info(f"Trained model saved to {output_path}")
            
            # Update current instance
            self.nlp = nlp
            self.add_entity_ruler_patterns()
            
        except Exception as e:
            logger.error(f"Error saving trained model: {e}")
            raise ModelUpdateError(f"Model saving failed: {e}")


# Example usage and testing
if __name__ == "__main__":
    try:
        # Create sample training data for testing
        dummy_training_data_path = DATA_DIR / "training" / "entity_training_data.jsonl"
        
        if not dummy_training_data_path.exists():
            sample_data = [
                {"text": "I need my pay slip for December 2023.", "entities": [(12, 20, "DOCUMENT_TYPE")]},
                {"text": "What is the sick leave policy?", "entities": [(12, 22, "LEAVE_TYPE")]},
                {"text": "My employee ID is EMP123.", "entities": [(18, 24, "EMPLOYEE_ID")]},
                {"text": "I work in the Finance department.", "entities": [(14, 21, "DEPARTMENT")]},
                {"text": "Can I check health insurance benefits?", "entities": [(12, 28, "BENEFIT_TYPE")]}
            ]
            
            dummy_training_data_path.parent.mkdir(parents=True, exist_ok=True)
            with open(dummy_training_data_path, 'w', encoding='utf-8') as f:
                for entry in sample_data:
                    json.dump(entry, f, ensure_ascii=False)
                    f.write('\n')
            
            logger.info(f"Created sample training data at {dummy_training_data_path}")

        # Initialize extractor
        extractor = EntityExtractor(model_path=MODEL_DIR)
        
        # Train model
        print("\n--- Starting Model Training ---")
        extractor.train_model(training_data_path=dummy_training_data_path, n_iter=20)
        print("--- Model Training Complete ---")

        # Test extraction
        print("\n=== Entity Extraction Results ===")
        test_texts = [
            "I need my pay slip for December 2023.",
            "What is the sick leave policy for software engineers?",
            "Can I check my health insurance benefits in Mumbai office?",
            "My employee ID is EMP123 and I work in the Finance department.",
            "I want to apply for maternity leave starting from January 2024."
        ]

        results = extractor.extract_entities(test_texts)
        
        for i, result in enumerate(results, 1):
            print(f"\nText {i}: '{result['text']}'")
            print(f"Processing Time: {result['processing_time']:.4f}s")
            print(f"Overall Confidence: {result['confidence_score']:.3f}")
            
            if result['entities']:
                for entity in result['entities']:
                    print(f"   • '{entity['text']}' -> {entity['label']} (confidence: {entity['score']:.3f})")
            else:
                print("   No entities found")

        # Performance statistics
        print(f"\n=== Performance Statistics ===")
        stats = extractor.get_performance_stats()
        for key, value in stats.items():
            print(f"{key}: {value}")

    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)