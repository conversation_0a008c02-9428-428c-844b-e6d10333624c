from .base import Chunking<PERSON>trategy, ChunkingConfig, TokenizerManager
import logging
from typing import List

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False
    logging.warning("sentence-transformers/sklearn not available - semantic chunking disabled")

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logging.warning("spacy not available - advanced NLP features disabled")

class SemanticStrategy(ChunkingStrategy):
    def __init__(self):
        self.model = None
        if SEMANTIC_AVAILABLE:
            try:
                self.model = SentenceTransformer('data/models_cache/Sentence Embedding Model')
            except Exception as e:
                logging.warning(f"Failed to load semantic model: {e}")

    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not self.model:
            from .token_aware import TokenAwareStrategy
            return TokenAwareStrategy().chunk_text(text, config, tokenizer_manager)
        sentences = self._split_into_sentences(text)
        if len(sentences) <= 1:
            return sentences
        embeddings = self.model.encode(sentences)
        chunks = []
        current_chunk_sentences = [sentences[0]]
        for i in range(1, len(sentences)):
            chunk_embedding = np.mean([embeddings[j] for j in range(len(current_chunk_sentences))], axis=0)
            similarity = cosine_similarity([chunk_embedding], [embeddings[i]])[0][0]
            test_chunk = " ".join(current_chunk_sentences + [sentences[i]])
            token_count = tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name)
            if similarity >= config.semantic_threshold and token_count <= config.max_tokens:
                current_chunk_sentences.append(sentences[i])
            else:
                chunks.append(" ".join(current_chunk_sentences))
                current_chunk_sentences = [sentences[i]]
        if current_chunk_sentences:
            chunks.append(" ".join(current_chunk_sentences))
        return chunks

    def _split_into_sentences(self, text: str) -> List[str]:
        if SPACY_AVAILABLE:
            try:
                nlp = spacy.load("en_core_web_sm")
                doc = nlp(text)
                return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
            except Exception as e:
                logging.warning(f"Spacy sentence splitting failed: {e}")
        from .token_aware import TokenAwareStrategy
        return TokenAwareStrategy()._split_into_sentences(text)

    def get_name(self) -> str:
        return "semantic" 