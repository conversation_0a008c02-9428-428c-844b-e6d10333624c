/**
 * Dynamic Greeting Message
 *
 * This script adds a personalized greeting message above the chat input box
 * that changes based on the time of day and includes the user's name.
 */

// Generic greetings for subsequent chats on the same day
const genericGreetings = [
    "How can I help you today?",
    "Let me know how I can support you.",
    "Need help with HR policies or benefits?",
    "I'm here to assist with your queries.",
    "What can I do for you today?",
    "Feel free to ask anything about your job or leaves.",
    "Here to help — just type your question.",
    "Let's make work smoother. What do you need?",
    "Happy to help with your HR concerns.",
    "Ask away, I'm listening."
];

document.addEventListener('DOMContentLoaded', () => {
    console.log('Dynamic greeting script loaded');

    // Initialize immediately (no delay)
    initDynamicGreeting();

    // Listen for login/logout events to update the greeting
    window.addEventListener('user-logged-in', () => {
        checkChatAndShowGreeting();
    });

    window.addEventListener('user-logged-out', () => {
        // Don't call hideGreetingMessage() on logout - that's for logged-in users
        // The pre-login UI will handle showing its own welcome container

        // Reset welcome title to default for non-logged-in users
        const welcomeTitle = document.getElementById('welcomeTitle');
        if (welcomeTitle) {
            welcomeTitle.innerHTML = '👋 Welcome to ZiaHR';
        }

        // Clear any existing greeting container for logged-in users
        const greetingContainer = document.getElementById('greetingMessageContainer');
        if (greetingContainer) {
            greetingContainer.remove();
        }
    });
    
    // Listen for new chat events to update the greeting
    window.addEventListener('new-chat-started', () => {
        const userData = JSON.parse(localStorage.getItem('user_data') || 'null');
        if (userData) {
            checkChatAndShowGreeting();
        }
    });
});

// We've removed the MutationObserver to improve performance

/**
 * Initialize the dynamic greeting
 */
function initDynamicGreeting() {
    try {
        // Check if user is logged in
        const userData = JSON.parse(localStorage.getItem('user_data') || 'null');

        if (userData) {
            console.log('User is logged in, initializing dynamic greeting');

            // Ensure greeting container exists and is accessible
            const greetingContainer = document.getElementById('greetingMessageContainer');
            if (!greetingContainer) {
                console.error('Greeting container not found in DOM');
                return;
            }

            // Force remove any conflicting styles that might hide the container
            greetingContainer.style.removeProperty('display');
            greetingContainer.style.removeProperty('visibility');
            greetingContainer.style.removeProperty('opacity');

            // Check if chat is empty and show greeting if needed
            checkChatAndShowGreeting();

            // Get user's first name for the welcome title
            let userName = 'there';
            if (userData && userData.email) {
                // Try to extract a name from the email (before the @ symbol)
                const emailName = userData.email.split('@')[0];
                // Capitalize the first letter
                userName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
            }

            // Get appropriate greeting (time-based or generic)
            const greeting = getAppropriateGreeting();

            // Update the welcome title
            updateWelcomeTitle(userName, greeting);
        } else {
            // Hide greeting if user is not logged in
            hideGreetingMessage();
        }
    } catch (error) {
        console.error('Error initializing dynamic greeting:', error);
    }
}

/**
 * Update the greeting message with user's name and appropriate greeting
 * @param {Object} userData - The user data object containing name information
 */
function updateGreetingMessage(userData) {
    try {
        // Get the greeting container
        let greetingContainer = document.getElementById('greetingMessageContainer');
        let greetingMessage = document.getElementById('greetingMessage');
        if (!greetingContainer || !greetingMessage) {
            console.error('Greeting container or message element not found.');
            return;
        }
        // Check if there are any user or bot messages in the chat
        const userMessages = document.querySelectorAll('.user-message, .bot-message');
        const hasMessages = userMessages.length > 0;
        // Add or remove has-messages class to chat-messages container
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            if (hasMessages) {
                chatMessages.classList.add('has-messages');
            } else {
                chatMessages.classList.remove('has-messages');
            }
        }
        // If there are messages, hide the greeting message container
        if (hasMessages) {
            greetingContainer.style.display = 'none';
            return;
        }
        // Set the greeting text
        let userName = 'there';
        if (userData && userData.email) {
            const emailName = userData.email.split('@')[0];
            userName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
        }
        
        // Get appropriate greeting (time-based or generic)
        const greeting = getAppropriateGreeting();
        
        // Format the greeting message
        if (isFirstChatToday()) {
            // Time-based greeting for first chat of the day
            greetingMessage.innerHTML = `${greeting}, <b>${userName}</b>! How can I help you today?`;
        } else {
            // Generic greeting for subsequent chats
            greetingMessage.innerHTML = `${greeting}`;
        }
        
        // Show the greeting container
        greetingContainer.style.display = 'block';
        greetingContainer.style.visibility = 'visible';
        greetingContainer.style.opacity = '1';
        // Add event listeners to suggestion chips if any
        addChipEventListeners();
    } catch (error) {
        console.error('Error updating greeting message:', error);
    }
}

/**
 * Hide the greeting message
 */
function hideGreetingMessage() {
    try {
        // If we're in the process of starting a new chat, don't hide the greeting
        if (window.isStartingNewChat) {
            console.log('Not hiding greeting because new chat is starting');
            return;
        }

        const greetingContainer = document.getElementById('greetingMessageContainer');
        if (!greetingContainer) {
            return;
        }

        // Check if there are any messages in the chat
        // If there are no messages, don't hide the greeting
        const userMessages = document.querySelectorAll('.user-message, .bot-message');
        if (userMessages.length === 0) {
            console.log('Not hiding greeting because chat is empty');
            // Make sure it's visible
            if (greetingContainer) {
                greetingContainer.style.display = 'block';
                greetingContainer.style.opacity = '1';
            }
            return;
        }

        // Instantly hide the greeting (no fade-out, no delay)
        greetingContainer.style.opacity = '0';
        greetingContainer.style.display = 'none';
        console.log('Greeting message instantly hidden because chat has messages');
        if (typeof window.ensureChatInputCentered === 'function') {
            window.ensureChatInputCentered();
        }
    } catch (error) {
        console.error('Error hiding greeting message:', error);
    }
}

/**
 * Get a greeting based on the current time of day with appropriate emoji
 * @returns {string} A time-appropriate greeting with emoji
 */
function getTimeBasedGreeting() {
    try {
        const hour = new Date().getHours();

        if (hour >= 5 && hour < 12) {
            return '🌅 Good morning';
        } else if (hour >= 12 && hour < 18) {
            return '☀️ Good afternoon';
        } else if (hour >= 18 && hour < 22) {
            return '🌆 Good evening';
        } else {
            return '🌙 Good evening';
        }
    } catch (error) {
        console.error('Error getting time-based greeting:', error);
        return '👋 Hello';
    }
}

/**
 * Get a random generic greeting
 * @returns {string} A random generic greeting
 */
function getRandomGenericGreeting() {
    try {
        const randomIndex = Math.floor(Math.random() * genericGreetings.length);
        return genericGreetings[randomIndex];
    } catch (error) {
        console.error('Error getting random generic greeting:', error);
        return 'How can I help you today?';
    }
}

/**
 * Get the appropriate greeting based on whether it's the first chat today
 * @returns {string} Time-based greeting for first chat, random generic for subsequent
 */
function getAppropriateGreeting() {
    const today = new Date().toISOString().slice(0, 10);
    const lastGreetingDate = localStorage.getItem('ziahr_last_greeting_date');
    const greetingType = localStorage.getItem('ziahr_greeting_type_today');
    if (lastGreetingDate !== today) {
        // New day: reset to time-based greeting
        setGreetingShownToday('time');
        return getTimeBasedGreeting();
    } else if (greetingType === 'time') {
        // First chat of the day: show time-based, then switch to generic
        setGreetingShownToday('generic');
        return getTimeBasedGreeting();
    } else {
        // All subsequent chats/page refreshes: show generic
        return getRandomGenericGreeting();
    }
}

/**
 * Check if chat is empty and show greeting if needed
 */
function checkChatAndShowGreeting() {
    try {
        const userData = JSON.parse(localStorage.getItem('user_data') || 'null');
        if (!userData) {
            console.log('User not logged in, not showing greeting');
            return;
        }
        const userMessages = document.querySelectorAll('.user-message, .bot-message');
        const hasMessages = userMessages.length > 0;
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            if (hasMessages) {
                chatMessages.classList.add('has-messages');
            } else {
                chatMessages.classList.remove('has-messages');
            }
        }
        let greetingContainer = document.getElementById('greetingMessageContainer');
        let userName = 'there';
        if (userData && userData.email) {
            const emailName = userData.email.split('@')[0];
            userName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
        }
        
        // Get appropriate greeting (time-based or generic)
        const greeting = getAppropriateGreeting();
        updateWelcomeTitle(userName, greeting);
        
        // Show greeting if chat is empty
        if (!hasMessages) {
            updateGreetingMessage(userData);
            
            // Mark that greeting has been shown today (only for first chat)
            if (isFirstChatToday()) {
                setGreetingShownToday();
                console.log('Time-based greeting shown (first chat today)');
            } else {
                console.log('Generic greeting shown (subsequent chat today)');
            }
            
            if (greetingContainer) {
                greetingContainer.style.display = 'block';
                greetingContainer.style.visibility = 'visible';
                greetingContainer.style.opacity = '1';
                greetingContainer.style.removeProperty('display');
                greetingContainer.style.removeProperty('visibility');
                greetingContainer.style.removeProperty('opacity');
                greetingContainer.style.display = 'block !important';
                console.log('Greeting container forced to be visible');
            }
        } else {
            hideGreetingMessage();
            console.log('Chat has messages, hiding greeting message');
        }
    } catch (error) {
        console.error('Error checking chat and showing greeting:', error);
    }
}

/**
 * Update the welcome title with dynamic greeting
 * @param {string} userName - The user's name
 * @param {string} greeting - The time-based greeting
 */
function updateWelcomeTitle(userName, greeting) {
    const welcomeTitle = document.getElementById('welcomeTitle');
    if (welcomeTitle && welcomeTitle.offsetParent !== null) {
        welcomeTitle.innerHTML = `👋 Good ${greeting}, <b>${userName}</b>`;
    }
    // Do nothing if not present or not visible
}

// Make functions available globally
window.updateGreetingMessage = updateGreetingMessage;
window.hideGreetingMessage = hideGreetingMessage;
window.checkChatAndShowGreeting = checkChatAndShowGreeting;
window.updateWelcomeTitle = updateWelcomeTitle;
window.getAppropriateGreeting = getAppropriateGreeting;

/**
 * Add click event listeners to suggestion chips.
 */
function addChipEventListeners() {
    // Add click event to both .chip and .suggestion-chip for compatibility
    const chips = document.querySelectorAll('.chip, .suggestion-chip');
    chips.forEach(chip => {
        // Remove previous event listeners by replacing with a clone
        const newChip = chip.cloneNode(true);
        chip.parentNode.replaceChild(newChip, chip);
        newChip.addEventListener('click', (event) => {
            const chipText = event.currentTarget.textContent.trim();
            console.log('Chip clicked:', chipText);
            // Assuming a global function `sendChatMessage` exists to send messages
            if (typeof window.sendChatMessage === 'function') {
                window.sendChatMessage(chipText);
            } else {
                console.error('sendChatMessage function is not defined.');
                // Fallback for demonstration: log the message
                alert(`Simulating sending message: ${chipText}`);
            }
            hideGreetingMessage(); // Hide greeting after a chip is clicked
        });
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing dynamic greeting...');
    initDynamicGreeting();
});

// Also initialize on window load as a fallback
window.addEventListener('load', () => {
    console.log('Window loaded, ensuring dynamic greeting is initialized...');
    // Only initialize if not already done
    const greetingContainer = document.getElementById('greetingMessageContainer');
    if (greetingContainer && greetingContainer.style.display === 'none') {
        console.log('Greeting container still hidden, re-initializing...');
        initDynamicGreeting();
    }
});

function isFirstChatToday() {
    const today = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const lastGreetingDate = localStorage.getItem('ziahr_last_greeting_date');
    const greetingType = localStorage.getItem('ziahr_greeting_type_today');
    // If it's a new day, reset greeting type
    if (lastGreetingDate !== today) {
        localStorage.setItem('ziahr_greeting_type_today', 'time');
        return true;
    }
    // If greeting type is not set or is 'time', treat as first chat
    return greetingType === 'time';
}

function setGreetingShownToday(type = 'time') {
    const today = new Date().toISOString().slice(0, 10);
    localStorage.setItem('ziahr_last_greeting_date', today);
    localStorage.setItem('ziahr_greeting_type_today', type);
}
