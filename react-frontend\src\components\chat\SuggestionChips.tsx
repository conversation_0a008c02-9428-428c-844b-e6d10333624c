import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Suggestion {
  text: string;
  query: string;
  icon?: string;
  category?: string;
}

interface SuggestionChipsProps {
  suggestions?: Suggestion[];
  onSuggestionClick: (query: string) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

const SuggestionChips: React.FC<SuggestionChipsProps> = ({
  suggestions,
  onSuggestionClick,
  className,
  variant = 'default'
}) => {
  const defaultSuggestions: Suggestion[] = [
    {
      text: "📋 Leave Policy",
      query: "What is the company's leave policy?",
      category: "Policies"
    },
    {
      text: "👥 Referral Program",
      query: "How does the employee referral program work?",
      category: "Programs"
    },
    {
      text: "👔 Dress Code",
      query: "What is the dress code policy?",
      category: "Policies"
    },
    {
      text: "🏠 Work from Home",
      query: "Tell me about the work from home policy",
      category: "Policies"
    },
    {
      text: "💼 Benefits",
      query: "What are the company benefits?",
      category: "Benefits"
    },
    {
      text: "📅 Request Time Off",
      query: "How do I request time off?",
      category: "Actions"
    },
  ];

  const chipSuggestions = suggestions || defaultSuggestions;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const chipVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    },
    tap: {
      scale: 0.95
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "flex flex-wrap gap-3",
        variant === 'compact' ? "gap-2" : "gap-3",
        className
      )}
    >
      {chipSuggestions.map((suggestion, index) => (
        <motion.div
          key={index}
          variants={chipVariants}
          whileHover="hover"
          whileTap="tap"
        >
          <button
            onClick={() => onSuggestionClick(suggestion.query)}
            className={cn(
              // Reduced padding, font, rounded for 15% smaller size, matching HRSuggestionButtons
              "suggestion-chip h-auto py-2.5 px-5 rounded-xl border-2 transition-all duration-200",
              "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600",
              "hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300",
              "focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none",
              "text-gray-700 dark:text-gray-300 font-medium shadow-sm hover:shadow-md text-base", // text-base instead of text-lg
              variant === 'compact' && "py-1.5 px-3 text-sm"
            )}
          >
            <span className="whitespace-nowrap">{suggestion.text}</span>
          </button>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default SuggestionChips;
