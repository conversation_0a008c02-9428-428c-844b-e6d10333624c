import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>spons<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#6366f1", "#10b981", "#f59e42", "#ef4444", "#6366a1", "#f472b6", "#fbbf24", "#0ea5e9", "#a3e635", "#f87171"];

interface TopicTrendsLineChartProps {
  trendingTopics: Array<{ topic: string; trend: number[] }>;
  topics: string[];
  loading?: boolean;
  error?: string | null;
}

const TopicTrendsLineChart: React.FC<TopicTrendsLineChartProps> = ({ trendingTopics, topics, loading, error }) => {
  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!trendingTopics || !trendingTopics.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  // Transform trendingTopics to chart data: [{date: 'Day 1', topic1: val, topic2: val, ...}, ...]
  const maxLen = Math.max(...trendingTopics.map(t => t.trend.length));
  const chartData = Array.from({ length: maxLen }).map((_, i) => {
    const entry: any = { date: `Day ${i + 1}` };
    trendingTopics.forEach(t => {
      entry[t.topic] = t.trend[i] ?? null;
    });
    return entry;
  });

  return (
    <div className="h-80 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 16, right: 16, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#444" />
          <XAxis dataKey="date" stroke="#888" />
          <YAxis stroke="#888" />
          <Tooltip
            content={({ active, payload, label }) => {
              if (active && payload && payload.length) {
                return (
                  <div className="bg-background dark:bg-zinc-900 border border-border dark:border-zinc-800 rounded shadow p-3 text-xs">
                    <div className="font-semibold mb-1">{label}</div>
                    <ul className="list-disc ml-4">
                      {payload.map((p: any, i: number) => (
                        <li key={i} style={{ color: p.color }}>
                          {p.name}: <span className="font-semibold">{p.value}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }
              return null;
            }}
          />
          <Legend />
          {topics.map((topic, idx) => (
            <Line
              key={topic}
              type="monotone"
              dataKey={topic}
              stroke={COLORS[idx % COLORS.length]}
              strokeWidth={2}
              dot={false}
              name={topic}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TopicTrendsLineChart; 