import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

// ============================================================================
// SKELETON COMPONENT TYPES
// ============================================================================

interface SkeletonProps {
  className?: string;
  variant?: 'default' | 'shimmer' | 'pulse';
  children?: React.ReactNode;
}

interface SkeletonLineProps {
  width?: string | number;
  height?: string | number;
  className?: string;
}

interface SkeletonCardProps {
  className?: string;
  showAvatar?: boolean;
  lines?: number;
  showButton?: boolean;
}

interface SkeletonTableProps {
  rows?: number;
  columns?: number;
  className?: string;
}

interface SkeletonChartProps {
  type?: 'bar' | 'line' | 'pie' | 'area';
  className?: string;
}

// ============================================================================
// BASE SKELETON COMPONENT
// ============================================================================

export const Skeleton: React.FC<SkeletonProps> = ({ 
  className, 
  variant = 'default',
  children,
  ...props 
}) => {
  const baseClasses = "bg-muted rounded animate-pulse";
  
  const variantClasses = {
    default: "animate-pulse",
    shimmer: "animate-shimmer bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%]",
    pulse: "animate-pulse-gentle",
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// ============================================================================
// SKELETON LINE COMPONENT
// ============================================================================

export const SkeletonLine: React.FC<SkeletonLineProps> = ({ 
  width = "100%", 
  height = "1rem",
  className 
}) => {
  return (
    <div style={{ width, height }}>
      <Skeleton className={cn("block w-full h-full", className)} />
    </div>
  );
};

// ============================================================================
// SKELETON CARD COMPONENT
// ============================================================================

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  className,
  showAvatar = false,
  lines = 3,
  showButton = false,
}) => {
  return (
    <div className={cn("p-6 border border-border rounded-lg bg-card", className)}>
      {/* Header with optional avatar */}
      <div className="flex items-center space-x-4 mb-4">
        {showAvatar && (
          <Skeleton className="h-12 w-12 rounded-full" />
        )}
        <div className="flex-1 space-y-2">
          <SkeletonLine width="60%" height="1.25rem" />
          <SkeletonLine width="40%" height="0.875rem" />
        </div>
      </div>
      
      {/* Content lines */}
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <SkeletonLine
            key={index}
            width={index === lines - 1 ? "75%" : "100%"}
            height="0.875rem"
          />
        ))}
      </div>
      
      {/* Optional button */}
      {showButton && (
        <div className="mt-6">
          <Skeleton className="h-10 w-24 rounded-md" />
        </div>
      )}
    </div>
  );
};

// ============================================================================
// SKELETON TABLE COMPONENT
// ============================================================================

export const SkeletonTable: React.FC<SkeletonTableProps> = ({
  rows = 5,
  columns = 4,
  className,
}) => {
  return (
    <div className={cn("border border-border rounded-lg overflow-hidden", className)}>
      {/* Table header */}
      <div className="bg-muted/50 p-4 border-b border-border">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <SkeletonLine key={index} height="1rem" width="80%" />
          ))}
        </div>
      </div>
      
      {/* Table rows */}
      <div className="divide-y divide-border">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <SkeletonLine 
                  key={colIndex} 
                  height="0.875rem" 
                  width={colIndex === 0 ? "90%" : "70%"} 
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// ============================================================================
// SKELETON CHART COMPONENT
// ============================================================================

export const SkeletonChart: React.FC<SkeletonChartProps> = ({
  type = 'bar',
  className,
}) => {
  const renderChart = () => {
    switch (type) {
      case 'bar':
        return (
          <div className="flex items-end justify-between h-48 px-4 pb-4">
            {Array.from({ length: 7 }).map((_, index) => (
              <div key={index} style={{ height: `${Math.random() * 80 + 20}%` }}>
                <Skeleton className="w-8 h-full" />
              </div>
            ))}
          </div>
        );
      
      case 'line':
        return (
          <div className="relative h-48 p-4">
            <Skeleton className="absolute inset-4 rounded-none">
              <svg className="w-full h-full opacity-30">
                <path
                  d="M 0 100 Q 50 50 100 80 T 200 60 T 300 90"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  className="text-primary"
                />
              </svg>
            </Skeleton>
          </div>
        );
      
      case 'pie':
        return (
          <div className="flex items-center justify-center h-48">
            <Skeleton className="w-32 h-32 rounded-full" />
          </div>
        );
      
      case 'area':
        return (
          <div className="relative h-48 p-4">
            <Skeleton className="absolute inset-4 rounded-none opacity-30" />
            <Skeleton className="absolute inset-4 rounded-none opacity-20" />
          </div>
        );
      
      default:
        return (
          <div className="h-48 p-4">
            <Skeleton className="w-full h-full" />
          </div>
        );
    }
  };

  return (
    <div className={cn("border border-border rounded-lg bg-card", className)}>
      {/* Chart header */}
      <div className="p-4 border-b border-border">
        <SkeletonLine width="40%" height="1.25rem" />
        <div className="mt-2">
          <SkeletonLine width="60%" height="0.875rem" />
        </div>
      </div>
      
      {/* Chart content */}
      {renderChart()}
      
      {/* Chart legend */}
      <div className="p-4 border-t border-border">
        <div className="flex flex-wrap gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <Skeleton className="w-3 h-3 rounded-full" />
              <SkeletonLine width="4rem" height="0.75rem" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// SKELETON DASHBOARD COMPONENT
// ============================================================================

export const SkeletonDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <SkeletonLine width="12rem" height="2rem" />
          <SkeletonLine width="20rem" height="1rem" />
        </div>
        <Skeleton className="h-10 w-32 rounded-md" />
      </div>
      
      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="p-6 border border-border rounded-lg bg-card">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <SkeletonLine width="4rem" height="0.875rem" />
                <SkeletonLine width="3rem" height="1.5rem" />
              </div>
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>
        ))}
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SkeletonChart type="bar" />
        <SkeletonChart type="line" />
      </div>
      
      {/* Table */}
      <SkeletonTable rows={8} columns={5} />
    </div>
  );
};

// ============================================================================
// ANIMATED SKELETON WRAPPER
// ============================================================================

interface AnimatedSkeletonProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}

export const AnimatedSkeleton: React.FC<AnimatedSkeletonProps> = ({
  children,
  delay = 0,
  className,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.3 }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Export all components
export default Skeleton;
