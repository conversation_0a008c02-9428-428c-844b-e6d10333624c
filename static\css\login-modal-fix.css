/**
 * Login Modal Width Fix
 * Increases the width and length of the login modal in the pre-login UI by 30%
 * Also includes QR code loading spinner animation
 */

/* QR Code Loading Spinner Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Reduce the width and height of the login/register modal for a more compact look */
.modern-login {
    max-width: 320px !important;
    width: 96% !important;
    min-width: 200px !important;
    min-height: unset !important;
    padding: 0 !important;
}

.modern-login .modal-body {
    padding: 2px 16px !important; /* Add more left/right padding for comfort */
    min-height: 60px !important;
}

.modern-login .form-group {
    margin-bottom: 0 !important; /* Remove space between fields */
}

.modern-login .form-group label {
    margin-bottom: 1px !important; /* Minimal space below label */
}

.modern-login .form-control {
    width: 100% !important;
    font-size: 13px !important;
    padding: 2px 10px 2px 4px !important; /* Even less padding */
    height: 22px !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.modern-login .btn {
    padding: 2px 2px !important;
    font-size: 13px !important;
    height: 22px !important;
    margin-top: 2px !important;
    margin-bottom: 2px !important;
}

.modern-login .modal-header {
    padding: 2px 4px !important;
}

.modern-login .modal-header h3 {
    font-size: 13px !important;
    margin-bottom: 2px !important;
}

.modern-login .login-message {
    font-size: 11px !important;
    padding: 1px !important;
    margin-bottom: 2px !important;
}

/* Adjust padding for larger width */
.modern-login .form-group {
    margin-bottom: 16px !important;
}

/* Make sure inputs fit properly with larger size */
.modern-login .form-control {
    width: 100% !important;
    box-sizing: border-box !important;
    font-size: 16px !important;
    padding: 10px 12px !important;
    height: 42px !important; /* Increased height */
}

/* Adjust button sizes for larger modal */
.modern-login .btn {
    padding: 10px 16px !important;
    font-size: 16px !important;
}

/* Ensure the modal is centered */
#loginModal, #registerModal {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Adjust footer padding if present */
.modern-login .modal-footer {
    padding: 12px 20px !important;
}

/* Adjust label size for larger modal */
.modern-login label {
    font-size: 16px !important;
    margin-bottom: 6px !important;
    display: block !important;
}

/* Ensure password toggle button fits in larger input */
.modern-login .password-input-wrapper {
    position: relative !important;
}

.modern-login .password-toggle-btn {
    position: absolute !important;
    right: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: none !important;
    border: none !important;
    cursor: pointer !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 28px !important;
    height: 28px !important;
    z-index: 2;
}

.modern-login .password-toggle-btn i {
    font-size: 18px !important;
}

/* Adjust forgot password link */
.modern-login .forgot-password {
    font-size: 14px !important;
    margin-top: 4px !important;
    display: inline-block !important;
}

/* Adjust login actions */
.modern-login .login-actions-container {
    margin-top: 2px !important;
    margin-bottom: 2px !important;
}

.modern-login .btn-text {
    font-size: 15px !important;
}

/* Adjust primary button */
.modern-login .btn-primary {
    margin-top: 8px !important;
    height: 44px !important; /* Taller button */
}

/* Improve overall appearance for larger modal */
.modern-login {
    border-radius: 10px !important;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15) !important;
}

/* Add more spacing between elements */
.modern-login .login-form {
    padding: 10px 0 !important;
}

/* Improve input field appearance */
.modern-login .form-control {
    border-radius: 6px !important;
    transition: border-color 0.2s ease-in-out !important;
}

.modern-login .form-control:focus {
    border-color: #4285f4 !important;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2) !important;
}

/* Improve button appearance */
.modern-login .btn-primary {
    border-radius: 6px !important;
    background-color: #4285f4 !important;
    border: none !important;
    font-weight: 500 !important;
    transition: background-color 0.2s ease-in-out !important;
}

.modern-login .btn-primary:hover {
    background-color: #3367d6 !important;
}

/* Improve close button */
.modern-login .icon-button {
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
