import React from "react";

export const Card = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div {...props} style={{ border: '1px solid #eee', borderRadius: 8, padding: 16, margin: 8 }}>
    {children}
  </div>
);

export const CardContent = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div {...props}>{children}</div>
);

export const CardHeader = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div {...props} style={{ fontWeight: 'bold', marginBottom: 8 }}>{children}</div>
);

export const CardTitle = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div {...props} style={{ fontSize: 18 }}>{children}</div>
); 