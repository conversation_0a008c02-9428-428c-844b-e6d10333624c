"""
Comprehensive pytest configuration and fixtures for the Advanced RAG Chatbot.
"""
import pytest
import tempfile
import shutil
import os
import json
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List

import numpy as np
import torch
from flask import Flask
from flask.testing import FlaskClient

# Import application components
from src.config import DATA_DIR, PROCESSED_DIR, RAW_DIR, DB_DIR
from src.chain.chain_builder import ChainBuilder
from src.document_processing.training_pipeline import TrainingPipeline
from src.database.vector_store import QdrantVectorStore
from src.database.user_db import UserModel, ConversationModel
from src.retrieval.vector_search import VectorSearch
from src.intent.intent_classifier import IntentClassifier
from src.ner.entity_extractor import EntityExtractor
from src.speech.speech_to_text import SpeechToText
from src.speech.text_to_speech import TextToSpeech
from user_authentication.user_authorisation import AuthService
from src.utils.logger import get_logger

# Configure pytest
pytest_plugins = ["pytest_asyncio"]

# Test configuration
TEST_CONFIG = {
    "test_db_path": "test_data/db",
    "test_processed_path": "test_data/processed",
    "test_raw_path": "test_data/raw",
    "test_models_path": "test_data/models",
    "test_logs_path": "test_data/logs"
}

@pytest.fixture(scope="session")
def test_config():
    """Global test configuration."""
    return TEST_CONFIG

@pytest.fixture(scope="session")
def test_dirs(test_config):
    """Create and clean up test directories."""
    # Create test directories
    for path in test_config.values():
        Path(path).mkdir(parents=True, exist_ok=True)
    
    yield test_config
    
    # Cleanup after all tests
    for path in test_config.values():
        if Path(path).exists():
            shutil.rmtree(path)

@pytest.fixture
def mock_env_vars():
    """Mock environment variables for testing."""
    env_vars = {
        "GROQ_API_KEY": "test_groq_key",
        "QDRANT_API_KEY": "test_qdrant_key",
        "QDRANT_URL": "https://test.qdrant.io",
        "QDRANT_COLLECTION_NAME": "test_collection",
        "JWT_SECRET": "test_jwt_secret",
        "SMTP_SERVER": "smtp.test.com",
        "SMTP_PORT": "587",
        "SMTP_USERNAME": "<EMAIL>",
        "SMTP_PASSWORD": "test_password",
        "SENDER_EMAIL": "<EMAIL>",
        "HR_EMAILS": "<EMAIL>,<EMAIL>",
        "ENABLE_EMAIL_ESCALATION": "true",
        "LOG_LEVEL": "DEBUG"
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars

@pytest.fixture
def sample_documents():
    """Sample documents for testing."""
    return [
        {
            "title": "Leave Policy",
            "content": "Employees are entitled to 20 days of annual leave per year. Sick leave is unlimited with proper documentation.",
            "source_file": "leave_policy.pdf",
            "file_type": "pdf"
        },
        {
            "title": "Dress Code Policy",
            "content": "Business casual attire is required Monday through Thursday. Casual Friday is permitted.",
            "source_file": "dress_code_policy.docx",
            "file_type": "docx"
        },
        {
            "title": "Remote Work Policy",
            "content": "Employees can work from home up to 3 days per week with manager approval.",
            "source_file": "remote_work_policy.txt",
            "file_type": "txt"
        }
    ]

@pytest.fixture
def sample_queries():
    """Sample queries for testing."""
    return [
        "What is the leave policy?",
        "How many sick days do I get?",
        "What is the dress code?",
        "Can I work from home?",
        "What is the employee referral bonus?",
        "How do I apply for leave?",
        "What is the termination policy?",
        "What are the working hours?"
    ]

@pytest.fixture
def sample_embeddings():
    """Sample embeddings for testing."""
    return np.random.rand(10, 768).astype(np.float32)

@pytest.fixture
def mock_vector_store():
    """Mock vector store for testing."""
    mock_store = Mock(spec=QdrantVectorStore)
    mock_store.search.return_value = [
        {
            "content": "Sample document content",
            "score": 0.85,
            "source_file": "test_doc.pdf",
            "chunk_id": "chunk_1"
        }
    ]
    mock_store.upload.return_value = None
    mock_store.get_collection_info.return_value = {
        "exists": True,
        "points_count": 100,
        "vector_size": 768
    }
    return mock_store

@pytest.fixture
def mock_embedding_generator():
    """Mock embedding generator for testing."""
    mock_gen = Mock()
    mock_gen.generate_embeddings.return_value = np.random.rand(5, 768).astype(np.float32)
    mock_gen.generate_query_embedding.return_value = np.random.rand(768).astype(np.float32)
    return mock_gen

@pytest.fixture
def mock_llm():
    """Mock LLM for testing."""
    mock_llm = Mock()
    mock_llm.invoke.return_value = Mock(content="This is a test response from the LLM.")
    return mock_llm

@pytest.fixture
def mock_intent_classifier():
    """Mock intent classifier for testing."""
    mock_classifier = Mock(spec=IntentClassifier)
    mock_classifier.classify.return_value = ("policy_inquiry", 0.85)
    return mock_classifier

@pytest.fixture
def mock_entity_extractor():
    """Mock entity extractor for testing."""
    mock_extractor = Mock(spec=EntityExtractor)
    mock_extractor.extract_entities.return_value = [
        {"text": "leave", "label": "POLICY_TYPE", "confidence": 0.9}
    ]
    return mock_extractor

@pytest.fixture
def mock_speech_to_text():
    """Mock speech-to-text for testing."""
    mock_stt = Mock(spec=SpeechToText)
    mock_stt.recognize_speech.return_value = "This is a test speech input"
    mock_stt.transcribe.return_value = {"text": "This is a test transcription"}
    return mock_stt

@pytest.fixture
def mock_text_to_speech():
    """Mock text-to-speech for testing."""
    mock_tts = Mock(spec=TextToSpeech)
    mock_tts.speak.return_value = None
    return mock_tts

@pytest.fixture
def mock_auth_service():
    """Mock authentication service for testing."""
    mock_auth = Mock(spec=AuthService)
    mock_auth.register_user.return_value = {
        "success": True,
        "message": "User registered successfully",
        "user_id": 1
    }
    mock_auth.login_user.return_value = {
        "success": True,
        "message": "Login successful",
        "token": "test_jwt_token",
        "user": {"id": 1, "email": "<EMAIL>", "full_name": "Test User"}
    }
    return mock_auth

@pytest.fixture
def mock_user_model():
    """Mock user model for testing."""
    mock_model = Mock(spec=UserModel)
    mock_model.create_user.return_value = 1
    mock_model.get_user_by_email.return_value = {
        "id": 1,
        "email": "<EMAIL>",
        "password_hash": "hashed_password",
        "full_name": "Test User",
        "two_fa_secret": "test_secret"
    }
    mock_model.get_user_by_id.return_value = {
        "id": 1,
        "email": "<EMAIL>",
        "full_name": "Test User"
    }
    return mock_model

@pytest.fixture
def mock_conversation_model():
    """Mock conversation model for testing."""
    mock_model = Mock(spec=ConversationModel)
    mock_model.save_conversation.return_value = 1
    mock_model.get_conversations.return_value = [
        {
            "user_query": "What is the leave policy?",
            "assistant_response": "Employees get 20 days of annual leave.",
            "language": "en",
            "query_timestamp": 1234567890.0
        }
    ]
    return mock_model

@pytest.fixture
def chain_builder(mock_vector_store, mock_embedding_generator, mock_llm, 
                  mock_intent_classifier, mock_entity_extractor):
    """Create a ChainBuilder instance with mocked dependencies."""
    with patch('src.chain.chain_builder.VectorSearch') as mock_vs, \
         patch('src.chain.chain_builder.EmbeddingGenerator') as mock_eg, \
         patch('src.chain.chain_builder.ChatGroq') as mock_cg, \
         patch('src.chain.chain_builder.IntentClassifier') as mock_ic, \
         patch('src.chain.chain_builder.EntityExtractor') as mock_ee:
        
        mock_vs.return_value = Mock()
        mock_eg.return_value = mock_embedding_generator
        mock_cg.return_value = mock_llm
        mock_ic.return_value = mock_intent_classifier
        mock_ee.return_value = mock_entity_extractor
        
        builder = ChainBuilder()
        builder.vector_search = mock_vector_store
        return builder

@pytest.fixture
def training_pipeline(mock_vector_store, mock_embedding_generator, mock_user_model):
    """Create a TrainingPipeline instance with mocked dependencies."""
    with patch('src.document_processing.training_pipeline.QdrantVectorStore') as mock_qs, \
         patch('src.document_processing.training_pipeline.EmbeddingGenerator') as mock_eg, \
         patch('src.document_processing.training_pipeline.DocumentModel') as mock_dm:
        
        mock_qs.return_value = mock_vector_store
        mock_eg.return_value = mock_embedding_generator
        mock_dm.return_value = mock_user_model
        
        pipeline = TrainingPipeline()
        return pipeline

@pytest.fixture
def flask_app(mock_env_vars):
    """Create a Flask test application."""
    from app import create_app
    
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    
    return app

@pytest.fixture
def client(flask_app):
    """Create a Flask test client."""
    return flask_app.test_client()

@pytest.fixture
def auth_headers():
    """Generate authentication headers for testing."""
    return {
        'Authorization': 'Bearer test_jwt_token',
        'Content-Type': 'application/json'
    }

@pytest.fixture
def sample_file():
    """Create a sample file for testing file uploads."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is a test document for file upload testing.")
        temp_file_path = f.name
    
    yield temp_file_path
    
    # Cleanup
    if os.path.exists(temp_file_path):
        os.unlink(temp_file_path)

@pytest.fixture
def sample_audio_data():
    """Create sample audio data for testing."""
    return np.random.rand(16000).astype(np.float32)  # 1 second at 16kHz

@pytest.fixture
def mock_api_responses():
    """Mock API responses for external services."""
    return {
        "groq_success": {
            "content": "This is a test response from Groq API.",
            "usage": {"total_tokens": 150}
        },
        "qdrant_success": {
            "status": "ok",
            "result": {"operation_id": 12345}
        },
        "email_success": {
            "success": True,
            "message_id": "test_message_id"
        }
    }

# Async test utilities
@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()

# Performance testing fixtures
@pytest.fixture
def performance_metrics():
    """Collect performance metrics during tests."""
    metrics = {
        "response_times": [],
        "memory_usage": [],
        "cpu_usage": [],
        "errors": []
    }
    return metrics

@pytest.fixture
def load_test_config():
    """Configuration for load testing."""
    return {
        "concurrent_users": 10,
        "requests_per_user": 100,
        "ramp_up_time": 30,  # seconds
        "test_duration": 300,  # seconds
        "target_rps": 50  # requests per second
    }

# Database testing fixtures
@pytest.fixture
def test_database():
    """Create a test database."""
    db_path = Path("test_data/test.db")
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    yield db_path
    
    # Cleanup
    if db_path.exists():
        db_path.unlink()

@pytest.fixture
def sample_conversation_data():
    """Sample conversation data for testing."""
    return {
        "device_id": "test_device_123",
        "user_query": "What is the leave policy?",
        "assistant_response": "Employees are entitled to 20 days of annual leave per year.",
        "language": "en",
        "sources": [
            {
                "content": "Leave policy content",
                "source_file": "leave_policy.pdf",
                "score": 0.85
            }
        ],
        "files_info": []
    }

# Mock external services
@pytest.fixture(autouse=True)
def mock_external_services():
    """Automatically mock external services in all tests."""
    with patch('requests.get'), \
         patch('requests.post'), \
         patch('smtplib.SMTP'), \
         patch('qdrant_client.QdrantClient'), \
         patch('groq.Groq'):
        yield

# Test data generators
class TestDataGenerator:
    """Generate test data for various scenarios."""
    
    @staticmethod
    def generate_documents(count: int = 5) -> List[Dict[str, Any]]:
        """Generate sample documents."""
        documents = []
        for i in range(count):
            documents.append({
                "title": f"Test Document {i+1}",
                "content": f"This is the content of test document {i+1}. " * 10,
                "source_file": f"test_doc_{i+1}.pdf",
                "file_type": "pdf"
            })
        return documents
    
    @staticmethod
    def generate_queries(count: int = 10) -> List[str]:
        """Generate sample queries."""
        base_queries = [
            "What is the leave policy?",
            "How do I apply for sick leave?",
            "What is the dress code?",
            "Can I work from home?",
            "What is the employee referral bonus?",
            "How do I request time off?",
            "What are the working hours?",
            "What is the termination process?",
            "How do I update my personal information?",
            "What are the company benefits?"
        ]
        return base_queries[:count]
    
    @staticmethod
    def generate_users(count: int = 5) -> List[Dict[str, Any]]:
        """Generate sample users."""
        users = []
        for i in range(count):
            users.append({
                "email": f"user{i+1}@test.com",
                "password": f"password{i+1}",
                "full_name": f"Test User {i+1}",
                "employee_id": f"EMP{i+1:03d}"
            })
        return users

@pytest.fixture
def test_data_generator():
    """Provide test data generator."""
    return TestDataGenerator()

# Cleanup utilities
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """Automatically clean up test files after each test."""
    yield
    # Cleanup test directories
    test_dirs = ["test_data", "logs"]
    for dir_name in test_dirs:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)

# Test markers
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as API test"
    )
    config.addinivalue_line(
        "markers", "database: mark test as database test"
    )
    config.addinivalue_line(
        "markers", "ml: mark test as machine learning test"
    ) 