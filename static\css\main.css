/* 
 * Main CSS Entry Point
 * Imports all modular CSS files in the correct order
 * 
 * IMPORTANT: Import order matters for CSS specificity and override behavior
 * Do not change the order without thorough testing
 */

/* 1. CSS Variables and Theme Definitions */
@import url('variables.css');

/* 2. Base Styles and Global Resets */
@import url('base.css');

/* 3. Layout and Container Styles */
@import url('layout.css');

/* 4. Sidebar Components */
@import url('sidebar.css');

/* 5. Chat and Message Styles */
@import url('chat.css');

/* 6. Input and Form Controls */
@import url('input.css');

/* 7. Button Styles and Interactions */
@import url('buttons.css');

/* 8. Modal and Dialog Styles */
@import url('modals.css');

/* 9. Form Controls and Settings */
@import url('forms.css');

/* 10. Notification Styles */
@import url('notifications.css');

/* 11. File Upload Styles */
@import url('file-upload.css');

/* 12. Animations and Transitions */
@import url('animations.css');

/* 13. Responsive Design */
@import url('responsive.css');

/* 14. Theme Overrides (must be last) */
@import url('themes.css');

/* 
 * Legacy Support Notice:
 * This file replaces the monolithic ziantrix-style-clean.css
 * All styles have been carefully migrated to maintain compatibility
 */
