import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  // Reduced h-10 -> h-8.5, px-4 -> px-3.5, py-2 -> py-1.5, text-sm -> text-base, rounded-xl -> rounded-lg
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-base font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-soft hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-soft hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-soft hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-soft hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        success: "bg-success-500 text-white shadow-soft hover:bg-success-600",
        warning: "bg-warning-500 text-white shadow-soft hover:bg-warning-600",
        ziahr: "bg-ziahr-700 text-white shadow-soft hover:bg-ziahr-800",
      },
      size: {
        default: "h-8.5 px-3.5 py-1.5", // 15% smaller
        sm: "h-6.8 rounded-md px-2.5 text-xs", // h-8 -> h-6.8, px-3 -> px-2.5
        lg: "h-10.2 rounded-lg px-6.8", // h-12 -> h-10.2, px-8 -> px-6.8
        xl: "h-11.9 rounded-xl px-8.5 text-base", // h-14 -> h-11.9, px-10 -> px-8.5
        icon: "h-8.5 w-8.5", // h-10 -> h-8.5
        "icon-sm": "h-6.8 w-6.8 rounded-md", // h-8 -> h-6.8
        "icon-lg": "h-10.2 w-10.2 rounded-lg", // h-12 -> h-10.2
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Loading...
          </>
        ) : (
          <>
            {leftIcon && <span className="mr-2">{leftIcon}</span>}
            {children}
            {rightIcon && <span className="ml-2">{rightIcon}</span>}
          </>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
