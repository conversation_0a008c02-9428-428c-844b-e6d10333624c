"""
SQLite database models for the Advanced RAG Chatbot.
"""

import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Optional

from ..utils.logger import get_logger
from ..config import CONVERSATION_DB_PATH, DOCUMENT_DB_PATH, USER_DB_PATH, ADMIN_USERS_DB_PATH

logger = get_logger(__name__)


class BaseDatabase:
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self._ensure_tables()

    def _get_connection(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.db_path, isolation_level=None)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_tables(self):
        raise NotImplementedError("Subclasses must implement table creation logic.")


class ConversationDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id TEXT NOT NULL,
                    device_id TEXT NOT NULL,
                    user_query TEXT NOT NULL,
                    assistant_response TEXT NOT NULL,
                    language TEXT NOT NULL,
                    query_timestamp TIMESTAMP NOT NULL,
                    response_timestamp TIMESTAMP NOT NULL,
                    response_time_seconds REAL NOT NULL,
                    feedback TEXT, -- 'like', 'dislike', or NULL
                    feedback_timestamp TIMESTAMP -- when feedback was given
                )
            ''')
            conn.commit()
            logger.info("✅ Conversations table initialized.")


class DocumentDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    source_file TEXT NOT NULL,
                    chunk_index INTEGER NOT NULL,
                    embedding_file TEXT,
                    created_at TIMESTAMP NOT NULL
                )
            ''')
            conn.commit()
            logger.info("✅ Documents table initialized.")


class UserDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    password_hash TEXT,
                    full_name TEXT NOT NULL,
                    employee_id TEXT UNIQUE,
                    two_fa_secret_user TEXT,
                    two_fa_secret_admin TEXT,
                    backup_codes_user TEXT,
                    backup_codes_admin TEXT,
                    role TEXT DEFAULT 'user',
                    active INTEGER DEFAULT 1,
                    created_at TIMESTAMP NOT NULL,
                    last_login TIMESTAMP
                )
            ''')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
            conn.commit()
            logger.info("✅ Users table initialized.")


class AdminUserDatabase(BaseDatabase):
    def _ensure_tables(self):
        # No-op: assume admin_users table already exists and is managed externally
        pass


class SessionDatabase(BaseDatabase):
    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    browser TEXT,
                    os TEXT,
                    device_fingerprint TEXT,
                    login_time TIMESTAMP,
                    last_activity TIMESTAMP,
                    logout_time TIMESTAMP,
                    location_country TEXT,
                    location_city TEXT,
                    latitude REAL,
                    longitude REAL,
                    auth_method TEXT,
                    success INTEGER DEFAULT 1,
                    user_type TEXT DEFAULT 'admin'
                )
            ''')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_type ON sessions(user_type)')
            conn.commit()
            logger.info("✅ Sessions table initialized.")


class SessionModel:
    def __init__(self, db_path=USER_DB_PATH):
        self.db = SessionDatabase(db_path)

    def create_session(self, id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, success=True, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO sessions (id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, success, user_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, int(success), user_type
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.exception("❌ Failed to create session")
            return False

    def get_active_sessions(self, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM sessions WHERE logout_time IS NULL AND user_type = ? ORDER BY last_activity DESC
                ''', (user_type,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch active sessions")
            return []

    def get_session_history(self, user_type="admin", limit=100, offset=0, filters=None):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM sessions WHERE user_type = ?'
                params = [user_type]
                if filters:
                    for key, value in filters.items():
                        if value is not None:
                            query += f' AND {key} = ?'
                            params.append(value)
                query += ' ORDER BY login_time DESC LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                cursor.execute(query, tuple(params))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch session history")
            return []

    def get_session_locations(self, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, user_id, device_fingerprint, login_time, location_country, location_city, latitude, longitude, user_type FROM sessions WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND user_type = ?
                ''', (user_type,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch session locations")
            return []

    def get_session_anomalies(self, user_type="admin", limit=100, offset=0):
        # Placeholder: In production, this would run anomaly detection logic
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                # Example: fetch failed logins, impossible travel, blacklisted IPs, etc.
                cursor.execute('''
                    SELECT * FROM sessions WHERE success = 0 AND user_type = ? ORDER BY login_time DESC LIMIT ? OFFSET ?
                ''', (user_type, limit, offset))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch session anomalies")
            return []


class ConversationModel:
    def __init__(self):
        self.db = ConversationDatabase(CONVERSATION_DB_PATH)

    def save_conversation(self, chat_id, device_id, user_query, assistant_response, language, query_timestamp, response_timestamp):
        try:
            response_time = round(response_timestamp - query_timestamp, 3)
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO conversations (chat_id, device_id, user_query, assistant_response, language, query_timestamp, response_timestamp, response_time_seconds)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    chat_id,
                    device_id,
                    user_query,
                    assistant_response,
                    language,
                    datetime.fromtimestamp(query_timestamp).isoformat(),
                    datetime.fromtimestamp(response_timestamp).isoformat(),
                    response_time
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.exception("❌ Failed to save conversation")
            return None

    def get_conversations(self, device_id=None, start_date=None, end_date=None, limit=100, offset=0):
        """
        Fetch conversations with optional filtering by device_id (user/session), date range, and pagination.
        Args:
            device_id: filter by user/session (optional)
            start_date: filter by start date (ISO string, optional)
            end_date: filter by end date (ISO string, optional)
            limit: max results
            offset: for pagination
        Returns:
            List of conversation dicts
        """
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM conversations WHERE 1=1'
                params = []
                if device_id:
                    query += ' AND device_id = ?'
                    params.append(device_id)
                if start_date:
                    query += ' AND query_timestamp >= ?'
                    params.append(start_date)
                if end_date:
                    query += ' AND query_timestamp <= ?'
                    params.append(end_date)
                query += ' ORDER BY query_timestamp DESC LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                cursor.execute(query, tuple(params))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch conversations")
            return []

    def get_chat_messages(self, chat_id, offset=0, limit=20):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM conversations WHERE chat_id = ? ORDER BY query_timestamp ASC LIMIT ? OFFSET ?
                ''', (chat_id, limit, offset))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch chat messages")
            return []

    def get_chat_message_count(self, chat_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) as count FROM conversations WHERE chat_id = ?
                ''', (chat_id,))
                result = cursor.fetchone()
                return result['count'] if result else 0
        except Exception as e:
            logger.exception("❌ Failed to get chat message count")
            return 0

    def set_feedback(self, conversation_id: int, feedback: str):
        """Set feedback ('like' or 'dislike') for a conversation message."""
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE conversations
                    SET feedback = ?, feedback_timestamp = ?
                    WHERE id = ?
                ''', (feedback, datetime.now().isoformat(), conversation_id))
                conn.commit()
                return True
        except Exception as e:
            logger.exception("❌ Failed to set feedback on conversation")
            return False


class DocumentModel:
    def __init__(self):
        self.db = DocumentDatabase(DOCUMENT_DB_PATH)

    def save_document(self, title, content, source_file, chunk_index, embedding_file=None):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO documents (title, content, source_file, chunk_index, embedding_file, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    title,
                    content,
                    source_file,
                    chunk_index,
                    embedding_file,
                    datetime.now().isoformat()
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.exception("❌ Failed to save document")
            return None


class UserModel:
    def __init__(self):
        self.db = UserDatabase(USER_DB_PATH)

    def create_user(self, email, password_hash, full_name, employee_id=None, two_fa_secret_user=None, two_fa_secret_admin=None, role='user'):
        # For admin users, password_hash is not required (OTP login only)
        if role != 'admin' and (not password_hash or password_hash.strip() == '' or password_hash.strip() == '.'):
            logger.error("Attempted to create user with empty or invalid password hash")
            return {'success': False, 'message': 'Password hash is empty or invalid. Registration failed.'}
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO users (email, password_hash, full_name, employee_id, two_fa_secret_user, two_fa_secret_admin, role, created_at, active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                ''', (
                    email,
                    password_hash,
                    full_name,
                    employee_id,
                    two_fa_secret_user,
                    two_fa_secret_admin,
                    role,
                    datetime.now().isoformat()
                ))
                conn.commit()
                return {'success': True, 'user_id': cursor.lastrowid}
        except sqlite3.IntegrityError:
            logger.warning(f"⚠️ User with email {email} already exists.")
            return {'success': False, 'message': 'User with this email already exists.'}
        except Exception as e:
            logger.exception("❌ Failed to create user")
            return {'success': False, 'message': f'Failed to create user: {str(e)}'}

    def get_user_by_email(self, email):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.exception("❌ Failed to get user by email")
            return None

    def get_user_by_id(self, user_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.exception("❌ Failed to get user by ID")
            return None

    def update_last_login(self, user_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users SET last_login = ? WHERE id = ?
                ''', (
                    datetime.now().isoformat(),
                    user_id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.exception("❌ Failed to update last login")
            return False

    def get_admin_users(self):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM users WHERE role IN ('admin', 'superadmin', 'viewer')")
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch admin users")
            return []


class AdminUserModel:
    def __init__(self):
        self.db = AdminUserDatabase(ADMIN_USERS_DB_PATH)

    def get_user_by_email(self, email):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM admin_users WHERE email = ?', (email,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.exception("❌ Failed to get admin user by email")
            return None

    def get_user_by_id(self, user_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM admin_users WHERE id = ?', (user_id,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.exception("❌ Failed to get admin user by ID")
            return None

    def get_admin_users(self):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM admin_users")
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("❌ Failed to fetch admin users")
            return []

    def create_user(self, email, full_name, role, role_level=None, tenant_id=None, created_at=None, last_login=None):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO admin_users (email, full_name, role, role_level, tenant_id, created_at, last_login, active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                ''', (
                    email, full_name, role, role_level, tenant_id, created_at, last_login
                ))
                conn.commit()
                return {'success': True, 'user_id': cursor.lastrowid}
        except Exception as e:
            logger.exception("❌ Failed to create admin user")
            return {'success': False, 'message': f'Failed to create admin user: {str(e)}'}

    def delete_user(self, user_id):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM admin_users WHERE id = ?', (user_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.exception("❌ Failed to delete admin user")
            return False
