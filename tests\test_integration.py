"""
Comprehensive integration tests for the Advanced RAG Chatbot.
"""
import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, <PERSON><PERSON>
import numpy as np

from src.chain.chain_builder import ChainBuilder
from src.document_processing.training_pipeline import TrainingPipeline
from src.retrieval.vector_search import VectorSearch
from src.database.vector_store import QdrantVectorStore
from user_authentication.user_authorisation import AuthService


class TestEndToEndDocumentProcessing:
    """Test end-to-end document processing pipeline."""
    
    def test_document_processing_pipeline(self, test_dirs, sample_documents):
        """Test complete document processing from file to vector store."""
        # Create test document
        test_doc_path = Path(test_dirs["test_raw_path"]) / "test_policy.txt"
        test_doc_path.write_text(sample_documents[0]["content"])
        
        with patch('src.document_processing.training_pipeline.QdrantVectorStore') as mock_qs, \
             patch('src.document_processing.training_pipeline.EmbeddingGenerator') as mock_eg, \
             patch('src.document_processing.training_pipeline.DocumentModel') as mock_dm:
            
            # Setup mocks
            mock_qs.return_value = Mock()
            mock_eg.return_value.generate_embeddings.return_value = np.random.rand(5, 768)
            mock_dm.return_value.save_document.return_value = 1
            
            pipeline = TrainingPipeline()
            result = pipeline.process_file(test_doc_path, force_reprocess=True)
            
            assert result > 0  # Should process at least one chunk
            mock_qs.return_value.upload.assert_called_once()
    
    def test_document_processing_with_multiple_formats(self, test_dirs):
        """Test processing documents in multiple formats."""
        formats = [
            ("test.txt", "This is a text document."),
            ("test.md", "# Test Document\n\nThis is a markdown document."),
        ]
        
        for filename, content in formats:
            test_doc_path = Path(test_dirs["test_raw_path"]) / filename
            test_doc_path.write_text(content)
            
            with patch('src.document_processing.training_pipeline.QdrantVectorStore') as mock_qs, \
                 patch('src.document_processing.training_pipeline.EmbeddingGenerator') as mock_eg, \
                 patch('src.document_processing.training_pipeline.DocumentModel') as mock_dm:
                
                mock_qs.return_value = Mock()
                mock_eg.return_value.generate_embeddings.return_value = np.random.rand(3, 768)
                mock_dm.return_value.save_document.return_value = 1
                
                pipeline = TrainingPipeline()
                result = pipeline.process_file(test_doc_path, force_reprocess=True)
                
                assert result > 0


class TestEndToEndQueryProcessing:
    """Test end-to-end query processing."""
    
    @pytest.mark.asyncio
    async def test_query_processing_pipeline(self, chain_builder, sample_queries):
        """Test complete query processing from input to response."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        # Setup all components
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Employees get 20 days of annual leave per year.",
            sources=[{"content": "Leave policy content", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Based on the policy, employees get 20 days of annual leave.")]
            
            result = await chain_builder.run_chain(query, device_id)
            
            # Verify complete pipeline
            assert result["content"] is not None
            assert result["language"] == "en"
            assert len(result["sources"]) > 0
            assert "response_time" in result
            assert result["intent"] is not None
            assert result["intent_confidence"] is not None
            assert len(result["entities"]) >= 0
    
    @pytest.mark.asyncio
    async def test_query_with_context_building(self, chain_builder, sample_queries):
        """Test query processing with context building."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        # Mock vector search results
        search_results = [
            {
                "content": "Employees are entitled to 20 days of annual leave per year.",
                "score": 0.85,
                "source_file": "leave_policy.pdf"
            },
            {
                "content": "Sick leave is unlimited with proper documentation.",
                "score": 0.75,
                "source_file": "leave_policy.pdf"
            }
        ]
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Employees are entitled to 20 days of annual leave per year. Sick leave is unlimited with proper documentation.",
            sources=search_results
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Based on the leave policy, employees get 20 days of annual leave and unlimited sick leave.")]
            
            result = await chain_builder.run_chain(query, device_id)
            
            # Verify context was built and used
            assert result["content"] is not None
            assert len(result["sources"]) == 2
            assert result["sources"][0]["score"] == 0.85
            assert result["sources"][1]["score"] == 0.75


class TestDatabaseIntegration:
    """Test database integration."""
    
    def test_user_registration_and_login(self, mock_auth_service, mock_user_model):
        """Test user registration and login flow."""
        # Test registration
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword",
            "full_name": "Test User",
            "employee_id": "EMP001"
        }
        
        result = mock_auth_service.register_user(**user_data)
        assert result["success"] == True
        assert result["user_id"] == 1
        
        # Test login
        login_result = mock_auth_service.login_user(
            email=user_data["email"],
            password=user_data["password"]
        )
        assert login_result["success"] == True
        assert "token" in login_result
        assert login_result["user"]["email"] == user_data["email"]
    
    def test_conversation_storage(self, mock_conversation_model, sample_conversation_data):
        """Test conversation storage and retrieval."""
        # Save conversation
        conversation_id = mock_conversation_model.save_conversation(
            device_id=sample_conversation_data["device_id"],
            user_query=sample_conversation_data["user_query"],
            assistant_response=sample_conversation_data["assistant_response"],
            language=sample_conversation_data["language"],
            query_timestamp=1234567890.0,
            response_timestamp=1234567890.5
        )
        
        assert conversation_id == 1
        
        # Retrieve conversations
        conversations = mock_conversation_model.get_conversations(
            sample_conversation_data["device_id"], 
            limit=10
        )
        
        assert len(conversations) == 1
        assert conversations[0]["user_query"] == sample_conversation_data["user_query"]
        assert conversations[0]["assistant_response"] == sample_conversation_data["assistant_response"]


class TestVectorSearchIntegration:
    """Test vector search integration."""
    
    def test_vector_search_pipeline(self, mock_vector_store, mock_embedding_generator):
        """Test complete vector search pipeline."""
        query = "What is the leave policy?"
        
        with patch('src.retrieval.vector_search.QdrantVectorStore') as mock_qs, \
             patch('src.retrieval.vector_search.EmbeddingGenerator') as mock_eg:
            
            mock_qs.return_value = mock_vector_store
            mock_eg.return_value = mock_embedding_generator
            
            vector_search = VectorSearch()
            
            # Mock search results
            mock_vector_store.search.return_value = [
                {
                    "content": "Employees get 20 days of annual leave.",
                    "score": 0.85,
                    "source_file": "leave_policy.pdf"
                }
            ]
            
            results = vector_search.search(query, top_k=5)
            
            assert len(results) == 1
            assert results[0]["score"] == 0.85
            assert "leave" in results[0]["content"].lower()
    
    def test_vector_search_with_prioritization(self, mock_vector_store, mock_embedding_generator):
        """Test vector search with file prioritization."""
        query = "What is the leave policy?"
        prioritize_files = ["leave_policy.pdf"]
        
        with patch('src.retrieval.vector_search.QdrantVectorStore') as mock_qs, \
             patch('src.retrieval.vector_search.EmbeddingGenerator') as mock_eg:
            
            mock_qs.return_value = mock_vector_store
            mock_eg.return_value = mock_embedding_generator
            
            vector_search = VectorSearch()
            
            # Mock search results with prioritization
            mock_vector_store.search.return_value = [
                {
                    "content": "Employees get 20 days of annual leave.",
                    "score": 0.85,
                    "source_file": "leave_policy.pdf"
                },
                {
                    "content": "General company information.",
                    "score": 0.75,
                    "source_file": "general_policy.pdf"
                }
            ]
            
            results = vector_search.search(query, top_k=5, prioritize_files=prioritize_files)
            
            assert len(results) == 2
            # Prioritized file should have boosted score
            assert results[0]["prioritized"] == True
            assert results[0]["score"] > 0.85  # Should be boosted


class TestAuthenticationIntegration:
    """Test authentication integration."""
    
    def test_jwt_token_flow(self, mock_auth_service, mock_user_model):
        """Test JWT token generation and verification."""
        # Register user
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword",
            "full_name": "Test User"
        }
        
        reg_result = mock_auth_service.register_user(**user_data)
        assert reg_result["success"] == True
        
        # Login and get token
        login_result = mock_auth_service.login_user(
            email=user_data["email"],
            password=user_data["password"]
        )
        assert login_result["success"] == True
        token = login_result["token"]
        
        # Verify token
        user = mock_auth_service.verify_token(token)
        assert user is not None
        assert user["email"] == user_data["email"]
    
    def test_2fa_flow(self, mock_auth_service, mock_user_model):
        """Test 2FA authentication flow."""
        # Register user with 2FA
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword",
            "full_name": "Test User"
        }
        
        reg_result = mock_auth_service.register_user(**user_data)
        assert reg_result["success"] == True
        assert "2fa_qr_url" in reg_result
        
        # Login without 2FA code
        login_result = mock_auth_service.login_user(
            email=user_data["email"],
            password=user_data["password"]
        )
        assert login_result["success"] == False
        assert login_result["message"] == "2FA code required"
        
        # Login with 2FA code
        mock_auth_service.verify_2fa_code.return_value = True
        login_result = mock_auth_service.login_user(
            email=user_data["email"],
            password=user_data["password"],
            two_fa_code="123456"
        )
        assert login_result["success"] == True


class TestErrorHandlingIntegration:
    """Test error handling across the system."""
    
    @pytest.mark.asyncio
    async def test_chain_error_propagation(self, chain_builder, sample_queries):
        """Test error propagation through the chain."""
        query = sample_queries[0]
        device_id = "test_device_123"
        
        # Simulate API failure
        chain_builder.api_status_checker.is_groq_operational.return_value = False
        
        result = await chain_builder.run_chain(query, device_id)
        
        assert "service is currently unavailable" in result["content"]
        assert result["error"]["type"] == "ServiceUnavailable"
    
    def test_document_processing_error_handling(self, test_dirs):
        """Test error handling in document processing."""
        # Create invalid file
        invalid_file = Path(test_dirs["test_raw_path"]) / "invalid.xyz"
        invalid_file.write_text("This is an invalid file type")
        
        with patch('src.document_processing.training_pipeline.QdrantVectorStore') as mock_qs, \
             patch('src.document_processing.training_pipeline.EmbeddingGenerator') as mock_eg, \
             patch('src.document_processing.training_pipeline.DocumentModel') as mock_dm:
            
            mock_qs.return_value = Mock()
            mock_eg.return_value = Mock()
            mock_dm.return_value = Mock()
            
            pipeline = TrainingPipeline()
            result = pipeline.process_file(invalid_file, force_reprocess=True)
            
            # Should handle invalid file gracefully
            assert result == 0  # No chunks processed
    
    def test_vector_search_error_handling(self, mock_vector_store, mock_embedding_generator):
        """Test error handling in vector search."""
        query = "What is the leave policy?"
        
        with patch('src.retrieval.vector_search.QdrantVectorStore') as mock_qs, \
             patch('src.retrieval.vector_search.EmbeddingGenerator') as mock_eg:
            
            mock_qs.return_value = mock_vector_store
            mock_eg.return_value = mock_embedding_generator
            
            vector_search = VectorSearch()
            
            # Simulate search failure
            mock_vector_store.search.side_effect = Exception("Search failed")
            
            results = vector_search.search(query, top_k=5)
            
            # Should return empty results on error
            assert len(results) == 0


class TestPerformanceIntegration:
    """Test performance across the system."""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_query_processing(self, chain_builder, sample_queries):
        """Test concurrent query processing."""
        import asyncio
        import time
        
        device_id = "test_device_123"
        
        # Setup mocks
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            # Process queries concurrently
            start_time = time.time()
            
            tasks = []
            for query in sample_queries[:5]:
                task = chain_builder.run_chain(query, device_id)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Verify all queries were processed
            assert len(results) == 5
            for result in results:
                assert result["content"] is not None
            
            # Should complete within reasonable time
            assert total_time < 5.0  # 5 seconds for 5 concurrent queries
    
    @pytest.mark.performance
    def test_bulk_document_processing(self, test_dirs, sample_documents):
        """Test bulk document processing performance."""
        import time
        
        # Create multiple test documents
        for i, doc in enumerate(sample_documents):
            test_doc_path = Path(test_dirs["test_raw_path"]) / f"test_doc_{i}.txt"
            test_doc_path.write_text(doc["content"])
        
        with patch('src.document_processing.training_pipeline.QdrantVectorStore') as mock_qs, \
             patch('src.document_processing.training_pipeline.EmbeddingGenerator') as mock_eg, \
             patch('src.document_processing.training_pipeline.DocumentModel') as mock_dm:
            
            mock_qs.return_value = Mock()
            mock_eg.return_value.generate_embeddings.return_value = np.random.rand(3, 768)
            mock_dm.return_value.save_document.return_value = 1
            
            pipeline = TrainingPipeline()
            
            start_time = time.time()
            
            total_processed = 0
            for i in range(len(sample_documents)):
                test_doc_path = Path(test_dirs["test_raw_path"]) / f"test_doc_{i}.txt"
                result = pipeline.process_file(test_doc_path, force_reprocess=True)
                total_processed += result
            
            end_time = time.time()
            total_time = end_time - start_time
            
            assert total_processed > 0
            assert total_time < 10.0  # 10 seconds for bulk processing


class TestSecurityIntegration:
    """Test security features."""
    
    def test_password_hashing(self, mock_auth_service):
        """Test password hashing and verification."""
        password = "testpassword"
        
        # Hash password
        hashed = mock_auth_service.hash_password(password)
        assert hashed != password
        assert len(hashed) > len(password)
        
        # Verify password
        assert mock_auth_service.verify_password(hashed, password) == True
        assert mock_auth_service.verify_password(hashed, "wrongpassword") == False
    
    def test_sensitive_data_handling(self, chain_builder, sample_queries):
        """Test handling of sensitive data in queries."""
        sensitive_queries = [
            "My salary is 50000 per month",
            "My PAN number is **********",
            "My Aadhaar is 123456789012"
        ]
        
        for query in sensitive_queries:
            # Should detect sensitive information
            assert contains_sensitive_info(query) == True
    
    def test_input_validation(self, chain_builder):
        """Test input validation across the system."""
        # Test empty input
        assert sanitize_input("") == ""
        assert sanitize_input("   ") == ""
        
        # Test input with special characters
        test_input = "Test\ninput\r\nwith\ttabs"
        sanitized = sanitize_input(test_input)
        assert "\n" not in sanitized
        assert "\r" not in sanitized
        assert "\t" not in sanitized


class TestScalabilityIntegration:
    """Test scalability features."""
    
    @pytest.mark.asyncio
    async def test_large_context_handling(self, chain_builder):
        """Test handling of large context."""
        # Create large query
        large_query = "What is the policy? " * 1000  # Very long query
        
        device_id = "test_device_123"
        
        chain_builder.api_status_checker.is_groq_operational.return_value = True
        chain_builder.context_builder.build_context.return_value = Mock(
            context="Sample context",
            sources=[{"content": "Sample source", "score": 0.85}]
        )
        chain_builder.history_manager.get_history.return_value = []
        
        with patch('src.chain.chain_builder.create_hr_assistant_prompt') as mock_prompt, \
             patch('asyncio.to_thread') as mock_thread:
            
            mock_prompt.return_value.invoke.return_value = "Formatted prompt"
            mock_thread.side_effect = ["Formatted prompt", Mock(content="Test response")]
            
            result = await chain_builder.run_chain(large_query, device_id)
            
            # Should handle large input gracefully
            assert result["content"] is not None
            assert "error" not in result
    
    def test_memory_efficient_processing(self, test_dirs, sample_documents):
        """Test memory-efficient document processing."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Create large document
        large_content = "This is a large document. " * 10000
        test_doc_path = Path(test_dirs["test_raw_path"]) / "large_document.txt"
        test_doc_path.write_text(large_content)
        
        with patch('src.document_processing.training_pipeline.QdrantVectorStore') as mock_qs, \
             patch('src.document_processing.training_pipeline.EmbeddingGenerator') as mock_eg, \
             patch('src.document_processing.training_pipeline.DocumentModel') as mock_dm:
            
            mock_qs.return_value = Mock()
            mock_eg.return_value.generate_embeddings.return_value = np.random.rand(50, 768)
            mock_dm.return_value.save_document.return_value = 1
            
            pipeline = TrainingPipeline()
            result = pipeline.process_file(test_doc_path, force_reprocess=True)
            
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable even for large documents
            assert memory_increase < 200 * 1024 * 1024  # 200MB
            assert result > 0 