import re
from typing import List, Tuple
from .base import ChunkingStrategy, ChunkingConfig, TokenizerManager

class HRStrategy(ChunkingStrategy):
    def __init__(self):
        self.hr_section_patterns = [
            r'^#{1,6}\s+(.+)$', r'^\d+\.\s+(.+)$', r'^[A-Z][A-Z\s]{2,}:(?:\s|$)',
            r'^(?:Policy|Procedure)\s*\d*:?\s*(.+)$', r'^(?:Q|Question):\s*(.+)$',
            r'^(?:Section|Chapter)\s+\d+', r'^(?:ARTICLE|Article)\s+[IVX\d]+',
            r'^(?:PART|Part)\s+[IVX\d]+', r'^(?:TITLE|Title)\s+[IVX\d]+',
            r'^\d+\.\d+\.?\s+', r'^[A-Z]\.\s+', r'^\([a-z]\)\s+', r'^\([0-9]+\)\s+'
        ]
        self.compiled_patterns = [re.compile(p, re.MULTILINE | re.IGNORECASE) 
                                  for p in self.hr_section_patterns]

    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        sections = self._split_by_hr_sections(text)
        chunks = []
        current_chunk = ""
        
        for header, content in sections:
            full_section = self._join_content(header, content)
            size_metric = (tokenizer_manager.count_tokens(full_section, config.tokenizer_name) 
                           if config.use_tokens else len(full_section))
            size_limit = config.max_tokens if config.use_tokens else config.chunk_size
            test_chunk = self._join_content(current_chunk, full_section)
            test_size = (tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) 
                         if config.use_tokens else len(test_chunk))
            if test_size <= size_limit:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                if size_metric > size_limit:
                    from .token_aware import TokenAwareStrategy
                    token_strategy = TokenAwareStrategy()
                    section_chunks = token_strategy.chunk_text(full_section, config, tokenizer_manager)
                    chunks.extend(section_chunks)
                    current_chunk = ""
                else:
                    current_chunk = full_section
        if current_chunk:
            chunks.append(current_chunk)
        return [chunk.strip() for chunk in chunks if chunk.strip()]

    def _split_by_hr_sections(self, text: str) -> List[Tuple[str, str]]:
        lines = text.split('\n')
        sections = []
        current_header = ""
        current_content = []
        for line in lines:
            stripped = line.strip()
            if not stripped:
                current_content.append(line)
                continue
            is_header = False
            for pattern in self.compiled_patterns:
                if pattern.match(stripped):
                    if current_header or current_content:
                        sections.append((current_header, '\n'.join(current_content)))
                    current_header = stripped
                    current_content = []
                    is_header = True
                    break
            if not is_header:
                current_content.append(line)
        if current_header or current_content:
            sections.append((current_header, '\n'.join(current_content)))
        return sections or [("", text)]

    def _join_content(self, chunk: str, new: str) -> str:
        if not chunk:
            return new
        if not new:
            return chunk
        return chunk + ("\n\n" if not chunk.endswith('\n') else "\n") + new

    def get_name(self) -> str:
        return "hr"

    def classify_section_header(self, header: str) -> str:
        # Stub for future metadata enrichment
        return "Unclassified"

    pass 