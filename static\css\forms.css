/* 
 * Form Controls and Input Elements
 * Contains styles for forms, login, settings, and input controls
 */

/* Base Form Styles */
.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    font-size: 12px;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 13px;
    background-color: var(--input-bg);
    color: var(--input-text);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
    font-family: inherit;
}

.form-control:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 3px rgba(108, 74, 182, 0.3);
}

/* Login Form Styles */
.login-form {
    margin-bottom: 10px;
}

.login-message {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 10px;
    text-align: center;
    font-weight: 500;
}

.theme-dark .login-message {
    color: #ff6b6b;
}

/* Modern Login Modal */
.modern-login .form-group {
    margin-bottom: 12px;
}

/* Password Input Wrapper */
.password-input-wrapper {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.password-toggle-btn:hover {
    color: var(--accent-color);
}

.password-input-wrapper input[type="password"],
.password-input-wrapper input[type="text"] {
    padding-right: 40px;
}

/* Register Link Container */
.register-link-container {
    text-align: center;
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 12px;
}

.register-link-container a {
    color: var(--accent-color);
    text-decoration: none;
    margin-left: 4px;
    font-weight: 500;
}

.register-link-container a:hover {
    text-decoration: underline;
    color: var(--accent-hover);
}

.login-actions-container {
    margin-top: 16px;
    text-align: center;
}

/* Search Form Styles */
.search-form .form-group {
    position: relative;
}

.search-form .form-control {
    width: 100%;
    padding: 10px 16px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--input-text);
    font-size: 14px;
}

.search-form .form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

/* Settings Styles */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.settings-tab {
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.settings-tab:hover {
    color: var(--text-primary);
}

.settings-tab.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
}

.settings-panel {
    display: none;
}

.settings-panel.active {
    display: block;
}

.settings-section {
    margin-bottom: 16px;
}

.settings-section h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.settings-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.settings-option label {
    font-size: 14px;
    color: var(--text-primary);
}

/* Settings Layout */
.settings-layout {
    display: flex;
    width: 100%;
    height: 100%;
}

.settings-sidebar {
    width: 140px;
    border-right: 1px solid var(--border-color);
    padding: 0;
    background-color: var(--bg-secondary);
    overflow-y: auto;
}

.settings-nav-item {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: var(--text-primary);
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.settings-nav-item:hover {
    background-color: rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.settings-nav-item.active {
    background-color: var(--accent-color);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-nav-item i {
    width: 14px;
    text-align: center;
    font-size: 12px;
}

.settings-content {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    background-color: var(--bg-primary);
}

/* Settings Items */
.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
    min-height: 48px;
}

.settings-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.settings-item-left {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    max-width: 60%;
}

.settings-item-right {
    display: flex;
    align-items: center;
}

.settings-item-right input[type="text"],
.settings-item-right input[type="email"] {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-size: 13px;
    background-color: var(--input-bg);
    color: var(--input-text);
    max-width: 200px;
}

.settings-description {
    padding: 0 20px 12px;
    border-bottom: 1px solid var(--border-color);
    box-sizing: border-box;
}

.settings-description p {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

/* Settings Buttons */
.settings-button {
    padding: 6px 12px;
    border-radius: 4px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.settings-button:hover {
    background-color: var(--bg-secondary);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-button:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

.settings-button.danger {
    color: #fff;
    border-color: #ef4146;
    background-color: #ef4146;
    font-weight: 600;
}

.settings-button.danger:hover {
    background-color: #d63031;
    border-color: #d63031;
}

/* Login Icon */
.login-icon {
    cursor: pointer;
    padding: 8px;
    transition: background-color 0.2s;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-icon i {
    font-size: 20px;
    color: #374151;
}

.login-icon:hover {
    background-color: var(--bg-secondary);
}

/* Dark Theme Form Styles */
.theme-dark .settings-sidebar {
    background-color: #2B2B2B;
    border-right: 1px solid #444654;
}

.theme-dark .settings-nav-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-nav-item.active {
    background-color: var(--accent-color);
    color: white;
}

.theme-dark .settings-content {
    background-color: #1E1E1E;
    color: var(--text-primary);
}

.theme-dark .settings-panel {
    background-color: #1E1E1E;
}

.theme-dark .settings-item:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

.theme-dark .settings-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-description {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-button {
    background-color: #343541;
    color: #FFFFFF;
    border-color: #444654;
}

.theme-dark .settings-button:hover {
    background-color: #444654;
}

.theme-dark .settings-item-right input[type="text"],
.theme-dark .settings-item-right input[type="email"] {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--input-border) !important;
}
