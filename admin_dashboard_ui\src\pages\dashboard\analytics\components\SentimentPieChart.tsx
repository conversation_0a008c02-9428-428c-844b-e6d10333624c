import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Toolt<PERSON>, Responsive<PERSON><PERSON><PERSON>, Legend } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#10b981", "#f59e42", "#ef4444"];

interface SentimentPieChartProps {
  sentimentDistribution: any[];
  loading: boolean;
  error?: string | Error | null;
}

const SentimentPieChart: React.FC<SentimentPieChartProps> = ({ sentimentDistribution, loading, error }) => {
  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{typeof error === 'string' ? error : error?.message}</div>;
  if (!sentimentDistribution || !sentimentDistribution.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  return (
    <div className="h-80 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={sentimentDistribution}
            dataKey="count"
            nameKey="sentiment"
            cx="50%"
            cy="50%"
            outerRadius={80}
            label
          >
            {sentimentDistribution.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default SentimentPieChart; 