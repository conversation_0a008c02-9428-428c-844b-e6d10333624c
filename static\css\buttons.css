/* 
 * <PERSON>ton Styles and Interactions
 * Contains all button styles, hover effects, and button-related components
 */

/* Base Button Styles */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Primary Button */
.btn-primary {
    background-color: var(--cta-color);
    color: white;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background-color: var(--cta-hover);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(255, 107, 107, 0.3);
}

.btn-primary:disabled {
    background-color: var(--button-disabled);
    cursor: not-allowed;
}

/* Secondary Button */
.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-secondary);
}

/* Block Button */
.btn-block {
    width: 100%;
    padding: 6px 8px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    display: block;
}

.btn-block:hover {
    background-color: var(--accent-hover);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* Text Button */
.btn-text {
    background: transparent;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    font-size: 12px;
    padding: 4px 8px;
    text-decoration: none;
}

.btn-text:hover {
    text-decoration: underline;
}

/* New Chat Button */
.new-chat-btn {
    background-color: #000000;
    color: #FFFFFF;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-dark .new-chat-btn {
    background-color: #FFFFFF;
    color: #000000;
}

.new-chat-btn:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.theme-dark .new-chat-btn:hover {
    box-shadow: 0 2px 5px rgba(255, 255, 255, 0.2);
}

/* Collapsed Sidebar New Chat Button */
.sidebar.collapsed .new-chat-btn {
    width: 30px;
    height: 30px;
    padding: 0;
    border-radius: 50%;
    min-width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-dark .sidebar.collapsed .new-chat-btn {
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none;
}

.theme-dark .sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none;
}

/* Send Button */
.send-btn {
    background-color: var(--cta-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
}

.send-btn:hover {
    background-color: var(--cta-hover);
    transform: none;
    box-shadow: none;
}

.send-btn:disabled {
    background-color: var(--button-disabled);
    cursor: not-allowed;
    box-shadow: none;
}

/* Action Buttons */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: var(--accent-color);
}

.action-btn-icon {
    width: 16px;
    height: 16px;
    display: block;
}

/* Input Action Buttons */
.input-action-btn, .action-btn {
    width: 22px !important;
    height: 22px !important;
    min-width: 22px !important;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-action-btn:hover {
    color: var(--accent-color);
}

/* Feedback Buttons */
.feedback-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
}

.feedback-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--accent-color);
    opacity: 1;
}

.feedback-btn.active {
    color: var(--accent-color);
    background-color: rgba(var(--accent-color-rgb), 0.1);
}

/* Message Feedback Buttons */
.message-feedback .feedback-btn {
    background-color: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-feedback .feedback-btn:hover {
    opacity: 1;
    background-color: var(--feedback-hover-bg);
}

.message-feedback .feedback-btn:active {
    transform: scale(0.95);
}

.message-feedback .feedback-btn i {
    pointer-events: none;
}

/* ChatGPT Style Buttons */
.action-btn-chatgpt {
    background: none;
    border: none;
    color: #6b6b6b;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn-chatgpt:hover {
    background: #ececec;
    color: #222;
}

.send-btn-chatgpt {
    background: #ececec;
    border: none;
    color: #b0b0b0;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.send-btn-chatgpt:enabled {
    background: #19c37d;
    color: #fff;
}

.send-btn-chatgpt:enabled .send-icon {
    filter: brightness(0) invert(1);
}

.send-btn-chatgpt:disabled {
    background: #ececec;
    color: #b0b0b0;
    cursor: not-allowed;
}

.send-btn-chatgpt .send-icon {
    width: 20px;
    height: 20px;
    display: block;
    margin: auto;
}

.send-btn-chatgpt .loading-icon {
    width: 20px;
    height: 20px;
    display: none;
    margin: auto;
}

/* Responsive Button Adjustments */
@media (max-width: 768px) {
    .action-btn {
        width: 20px !important;
        height: 20px !important;
        min-width: 20px !important;
    }

    .send-btn {
        width: 28px !important;
        height: 28px !important;
        min-width: 28px !important;
    }
}

@media (max-width: 480px) {
    .action-btn {
        width: 18px !important;
        height: 18px !important;
        min-width: 18px !important;
    }

    .send-btn {
        width: 26px !important;
        height: 26px !important;
        min-width: 26px !important;
    }
}
