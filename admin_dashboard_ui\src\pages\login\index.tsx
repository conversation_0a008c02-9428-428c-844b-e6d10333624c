import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { requestOtp, login as loginApi } from '@/services/api';
import { useAuthStore } from '../../hooks/useAuthStore';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtp, setShowOtp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | Error | null>('');
  const navigate = useNavigate();
  const setToken = useAuthStore((state) => state.setToken);
  const setUser = useAuthStore((state) => state.setUser);
  const setRole = useAuthStore((state) => state.setRole);

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      await requestOtp(email);
      setShowOtp(true);
    } catch (err: any) {
      let message = "Failed to send OTP";
      if (err?.response?.data?.detail) {
        message = err.response.data.detail;
      } else if (err?.message) {
        message = err.message;
      }
      setError(message);
      // Log error for debugging
      console.error("OTP request error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const res = await loginApi(email, otp);
      if (res.data.success && res.data.token) {
        setToken(res.data.token); // Save the JWT token for authenticated requests
        // Fetch user info using the token
        const userRes = await fetch('/api/admin-users/me', {
          headers: { 'Authorization': `Bearer ${res.data.token}` }
        });
        if (userRes.ok) {
          const userData = await userRes.json();
          setUser(userData);
          setRole(userData.role);
        }
        navigate('/dashboard');
      } else {
        setError(res.data.message || 'Invalid OTP');
      }
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Invalid OTP');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-accent-400/30 to-neutral-900 px-4">
      <div className="w-full max-w-2xl flex rounded-3xl shadow-2xl overflow-hidden border border-neutral-800 bg-neutral-950">
        {/* Left: Welcome/Branding */}
        <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-br from-accent-400/40 to-accent-600/20 p-8">
          <div className="text-4xl font-extrabold bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-4 text-center">
            ZiaHR Admin
          </div>
          <div className="text-lg text-neutral-200 text-center mb-2 font-semibold">
            Welcome Back!
          </div>
          <div className="text-neutral-400 text-center">
            Sign in to access your admin dashboard and manage your organization securely.
          </div>
        </div>
        {/* Right: Login Form */}
        <form
          onSubmit={showOtp ? handleVerifyOtp : handleSendOtp}
          className="flex-1 flex flex-col gap-6 p-10 justify-center"
        >
          <div className="text-3xl font-extrabold text-center bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-2 md:hidden">
            ZiaHR Admin Login
          </div>
          <div className="text-neutral-400 text-center mb-4 md:hidden">Sign in to your admin account</div>
          <input
            type="email"
            placeholder="Admin Email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
            required
            disabled={showOtp}
          />
          {showOtp && (
            <input
              type="text"
              placeholder="OTP"
              value={otp}
              onChange={e => setOtp(e.target.value)}
              className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
              required
              autoFocus
            />
          )}
          {error && <div className="text-red-500 p-2">{typeof error === 'string' ? error : error?.message}</div>}
          <button
            type="submit"
            className="bg-accent-400 hover:bg-accent-600 text-neutral-900 font-bold rounded py-2 text-lg transition"
            disabled={loading}
          >
            {loading ? (showOtp ? 'Verifying...' : 'Send OTP') : (showOtp ? 'Verify OTP' : 'Send OTP')}
          </button>
          <div className="flex justify-between text-xs text-neutral-500 mt-2">
            <a href="#" className="hover:underline">Forgot password?</a>
            <a href="#" className="hover:underline" onClick={e => { e.preventDefault(); navigate('/register'); }}>Create Account</a>
          </div>
          <div className="text-xs text-neutral-500 text-center mt-2">
            Only admin users are allowed. Unauthorized access is prohibited.
          </div>
        </form>
      </div>
    </div>
  );
} 