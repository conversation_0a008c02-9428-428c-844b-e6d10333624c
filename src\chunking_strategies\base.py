import re
import unicodedata
import hashlib
from typing import List, Dict, Any, Optional, Tu<PERSON>, Literal
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import logging

# Optional imports with fallbacks
try:
    from transformers import AutoTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("transformers not available - advanced tokenization disabled")

@dataclass
class ChunkingConfig:
    chunk_size: int = 800
    chunk_overlap: int = 150
    min_chunk_size: int = 100
    max_chunk_size: Optional[int] = None
    hr_mode: bool = True
    preserve_formatting: bool = True
    use_tokens: bool = True  # Default to True
    tokenizer_name: str = "cl100k_base"
    max_tokens: int = 512
    token_overlap: int = 50
    adaptive: bool = False
    temperature_overlap: bool = False
    strategy: Literal["auto", "token", "semantic", "recursive", "sentence", "hr"] = "auto"
    enable_async: bool = True
    max_workers: int = 4
    cache_tokenization: bool = True
    language: str = "auto"
    spacy_model: str = "en_core_web_sm"
    semantic_threshold: float = 0.7
    preserve_paragraphs: bool = True
    respect_word_boundaries: bool = True

    def __post_init__(self):
        if not self.use_tokens:
            logging.warning("Character-based chunking is deprecated. Please use token-based chunking for LLM compatibility.")
        if self.chunk_overlap >= self.chunk_size:
            raise ValueError("chunk_overlap must be less than chunk_size")
        if self.min_chunk_size <= 0:
            raise ValueError("min_chunk_size must be positive")
        if self.max_chunk_size is None:
            self.max_chunk_size = self.chunk_size + self.chunk_overlap
        if self.token_overlap >= self.max_tokens:
            raise ValueError("token_overlap must be less than max_tokens")

class TokenizerManager:
    def __init__(self):
        self._tokenizers = {}
        self._cache = {}
    def get_tokenizer(self, name: str):
        if name not in self._tokenizers:
            if TRANSFORMERS_AVAILABLE:
                try:
                    self._tokenizers[name] = AutoTokenizer.from_pretrained('data/models_cache/Sentence Embedding Model')
                except Exception as e:
                    logging.warning(f"Failed to load tokenizer from local cache: {e}")
                    self._tokenizers[name] = None
            else:
                self._tokenizers[name] = None
        return self._tokenizers[name]
    def count_tokens(self, text: str, tokenizer_name: str) -> int:
        cache_key = hashlib.md5(f"{tokenizer_name}:{text}".encode()).hexdigest()
        if cache_key in self._cache:
            return self._cache[cache_key]
        tokenizer = self.get_tokenizer(tokenizer_name)
        if tokenizer is None:
            count = len(text) // 4
        else:
            if hasattr(tokenizer, 'encode'):
                count = len(tokenizer.encode(text))
            else:
                count = len(tokenizer.tokenize(text))
        self._cache[cache_key] = count
        return count
    def encode_with_boundaries(self, text: str, tokenizer_name: str) -> List[Tuple[int, int]]:
        tokenizer = self.get_tokenizer(tokenizer_name)
        if tokenizer is None or not hasattr(tokenizer, 'encode_with_offsets'):
            words = text.split()
            boundaries = []
            pos = 0
            for word in words:
                start = text.find(word, pos)
                boundaries.append((start, start + len(word)))
                pos = start + len(word)
            return boundaries
        return tokenizer.encode_with_offsets(text)

class ChunkingStrategy(ABC):
    @abstractmethod
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        pass
    @abstractmethod
    def get_name(self) -> str:
        pass 