import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User as UserIcon, Settings, LogOut, ChevronDown } from 'lucide-react';
import { User } from '@/types';
import { cn } from '@/lib/utils';

interface HeaderProps {
  user: User | null;
  onOpenSettings: () => void;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({
  user,
  onOpenSettings,
  onLogout,
}) => {
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleUserMenuClick = () => {
    setShowUserDropdown(!showUserDropdown);
  };

  const handleSettingsClick = () => {
    setShowUserDropdown(false);
    onOpenSettings();
  };

  const handleLogoutClick = () => {
    setShowUserDropdown(false);
    onLogout();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className="h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center z-50">
      <div className="flex justify-between items-center w-full px-6">
        {/* Left section */}
        <div className="flex items-center gap-2">
          {/* Replace logo and new chat button with ZiaHR text */}
          <span className="text-2xl font-bold text-blue-700 tracking-tight select-none">ZiaHR</span>
        </div>

        {/* Right section - Share menu and User menu */}
        <div className="flex items-center gap-4">
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={handleUserMenuClick}
              className={cn(
                "flex items-center gap-2 px-3 py-2 rounded-xl transition-colors",
                "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              )}
              title="Account menu"
            >
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                <UserIcon className="w-4 h-4 text-gray-600 dark:text-gray-300" />
              </div>
              <span className="hidden sm:block font-medium">
                {user?.fullName || 'User'}
              </span>
              <ChevronDown className={cn(
                "w-4 h-4 transition-transform duration-200",
                showUserDropdown && "rotate-180"
              )} />
            </button>

            {/* User Dropdown */}
            <AnimatePresence>
              {showUserDropdown && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className={cn(
                    "absolute right-0 top-full mt-2 w-64 bg-white dark:bg-gray-800",
                    "border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg",
                    "py-2 z-50"
                  )}
                >
                  {/* User Info */}
                  <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {user?.fullName || 'User'}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {user?.email}
                    </div>
                    {user?.employeeId && (
                      <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        ID: {user.employeeId}
                      </div>
                    )}
                  </div>

                  {/* Menu Items */}
                  <div className="py-1">
                    <button
                      onClick={handleSettingsClick}
                      className="w-full flex items-center gap-3 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <Settings className="w-4 h-4" />
                      <span>Settings</span>
                    </button>
                    <button
                      onClick={handleLogoutClick}
                      className="w-full flex items-center gap-3 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Log out</span>
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
