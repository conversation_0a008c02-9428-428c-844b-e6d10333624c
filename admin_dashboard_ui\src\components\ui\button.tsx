import React from "react";

type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  variant?: 'default' | 'outline' | 'ghost' | 'link' | string;
  size?: 'sm' | 'md' | 'lg' | 'icon' | string;
};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, variant = 'default', size = 'md', className = '', ...props }, ref) => {
    const variantClass =
      variant === 'outline' ? 'border border-gray-300 bg-white' :
      variant === 'ghost' ? 'bg-transparent hover:bg-gray-100' :
      variant === 'link' ? 'bg-transparent underline text-blue-600' :
      'bg-blue-600 text-white';
    const sizeClass =
      size === 'sm' ? 'px-2 py-1 text-sm' :
      size === 'lg' ? 'px-6 py-3 text-lg' :
      size === 'icon' ? 'p-2 w-8 h-8 flex items-center justify-center' :
      'px-4 py-2';
    return (
      <button
        ref={ref}
        className={`rounded ${variantClass} ${sizeClass} ${className}`.trim()}
        {...props}
      >
        {children}
      </button>
    );
  }
);
Button.displayName = 'Button'; 