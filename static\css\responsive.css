/* 
 * Responsive Design and Media Queries
 * Contains responsive breakpoints and mobile-specific styles
 */

/* Large Desktop Screens */
@media (max-width: 1200px) {
    .app-container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }
    
    .sidebar {
        min-width: 180px;
        max-width: 220px;
    }
}

/* Tablet and Small Desktop */
@media (max-width: 992px) {
    .sidebar {
        position: absolute;
        z-index: 1002;
        height: 100vh;
        left: 0;
        top: 0;
        width: 80vw;
        max-width: 320px;
        min-width: 0;
        box-shadow: 2px 0 8px rgba(0,0,0,0.08);
        background: var(--sidebar-bg);
    }
    
    .sidebar.collapsed {
        width: 0 !important;
        min-width: 0 !important;
        max-width: 0 !important;
    }
    
    .app-container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }
    
    .chat-container {
        width: 100vw;
        min-width: 0;
        margin: 0;
        padding: 0;
    }
}

/* Mobile Landscape and Small Tablets */
@media (max-width: 768px) {
    .sidebar {
        width: 90vw;
        max-width: 320px;
        min-width: 120px;
        left: -100vw;
        top: 0;
        height: 100vh;
        z-index: 2000;
        transition: left 0.3s;
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        left: 0;
        transform: translateX(0);
    }
    
    .chat-container {
        width: 100vw;
        min-width: 0;
        margin: 0;
        padding: 0;
    }
    
    .greeting-message-container {
        position: absolute;
        max-width: 95%;
        width: 90%;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 16px;
    }
    
    .suggestion-chip {
        font-size: 13px !important;
        min-width: auto !important;
        width: auto !important;
    }
    
    .chat-input-container {
        position: fixed !important;
        bottom: 24px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 90%;
        max-width: 90%;
        padding: 12px;
    }
    
    .chatgpt-input-wrapper {
        padding: 14px !important;
        min-height: 70px !important;
    }
    
    .chatgpt-tool-btn {
        width: 28px !important;
        height: 28px !important;
        min-width: 28px !important;
        font-size: 13px !important;
        padding: 5px 6px !important;
    }
    
    .modal-content, .modal-lg {
        max-width: 98vw !important;
        width: 98vw !important;
        min-width: 0;
        max-height: 98vh !important;
        overflow-y: auto;
        border-radius: 10px;
        padding: 8px 4px;
    }
    
    .settings-sidebar {
        width: 120px;
    }
    
    .settings-nav-item {
        padding: 6px 8px;
        font-size: 12px;
        gap: 6px;
    }
    
    .settings-nav-item i {
        width: 12px;
        font-size: 11px;
    }
    
    .settings-item {
        padding: 10px 16px;
        min-height: 38px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .settings-item-left {
        font-size: 13px;
        max-width: 100%;
    }
    
    .settings-item-right {
        width: 100%;
        justify-content: flex-end;
    }
    
    .form-control {
        max-width: 150px;
        font-size: 11px;
    }
    
    .settings-button {
        font-size: 11px;
        padding: 5px 10px;
        min-width: 70px;
    }
    
    .input-suggestions {
        gap: 8px !important;
        padding: 0 12px;
    }
    
    .input-suggestions .suggestion-chip {
        font-size: 13px !important;
        padding: 6px 12px !important;
    }
}

/* Mobile Portrait */
@media (max-width: 576px) {
    .sidebar {
        width: 100vw;
        min-width: 0;
        max-width: 100vw;
        left: -100vw;
        top: 0;
        height: 100vh;
        z-index: 2000;
        transition: left 0.3s;
    }
    
    .sidebar.active {
        left: 0;
    }
    
    .chat-container {
        width: 100vw;
        min-width: 0;
        padding: 0 2vw;
        box-sizing: border-box;
    }
    
    .greeting-message-container {
        position: absolute;
        max-width: 98%;
        width: 95%;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 12px;
    }
    
    .suggestion-chip {
        font-size: 12px !important;
        min-width: auto !important;
        width: auto !important;
    }
    
    .chat-input-container {
        position: fixed !important;
        bottom: 24px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 95%;
        max-width: 95%;
        padding: 12px;
    }
    
    .chatgpt-input-wrapper {
        padding: 12px !important;
        min-height: 60px !important;
    }
    
    .modal-content, .modal-lg {
        max-width: 100vw !important;
        width: 100vw !important;
        min-width: 0;
        max-height: 100vh !important;
        overflow-y: auto;
        border-radius: 8px;
        padding: 4px 2px;
    }
    
    .input-suggestions {
        gap: 6px !important;
        padding: 0 8px;
    }
    
    .input-suggestions .suggestion-chip {
        font-size: 12px !important;
        padding: 5px 10px !important;
    }
}

/* Small Mobile Screens */
@media (max-width: 480px) {
    .chat-input-container {
        bottom: 8px !important;
        padding: 0 12px !important;
    }
    
    .chatgpt-input-wrapper {
        padding: 10px !important;
        min-height: 50px !important;
    }
    
    .chatgpt-tool-btn {
        width: 24px !important;
        height: 24px !important;
        min-width: 24px !important;
        font-size: 11px !important;
        padding: 4px !important;
    }
    
    .chatgpt-send-btn {
        width: 28px !important;
        height: 28px !important;
        min-width: 28px !important;
    }
    
    .action-btn {
        width: 18px !important;
        height: 18px !important;
        min-width: 18px !important;
        font-size: 14px !important;
        margin: 0 2px 0 0 !important;
    }
    
    .send-btn {
        width: 26px !important;
        height: 26px !important;
        min-width: 26px !important;
        margin: 0 0 0 4px !important;
    }
}

/* Very Small Screens */
@media (max-width: 400px) {
    .chat-input-container, 
    .input-main-area, 
    .input-actions-row, 
    .greeting-message-container, 
    .welcome-message {
        font-size: 0.85rem;
        padding: 0 1vw;
    }
    
    .greeting-message-container {
        max-width: 100vw;
        padding: 4vw 1vw 2vw 1vw;
        font-size: 0.95rem;
    }
    
    .chat-input-container {
        padding: 0 1vw 1vw 1vw;
    }
    
    .chat-input-form {
        max-width: 100vw;
    }
    
    .input-main-area textarea {
        font-size: 0.9rem;
        padding: 5vw 1vw;
    }
}

/* Responsive Button Adjustments */
@media (max-width: 768px) {
    .action-btn {
        width: 20px !important;
        height: 20px !important;
        min-width: 20px !important;
    }

    .send-btn {
        width: 28px !important;
        height: 28px !important;
        min-width: 28px !important;
    }
}

@media (max-width: 480px) {
    .action-btn {
        width: 18px !important;
        height: 18px !important;
        min-width: 18px !important;
    }

    .send-btn {
        width: 26px !important;
        height: 26px !important;
        min-width: 26px !important;
    }
}

/* Responsive Modal Adjustments */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }

    #settingsModal .modal-content {
        width: 95%;
        max-width: none;
        height: 90vh;
        max-height: 90vh;
    }

    .dropdown-select {
        font-size: 11px;
        min-width: 90px;
    }
}

@media (max-width: 480px) {
    .modal-content, .modal-lg {
        max-width: 100vw !important;
        width: 100vw !important;
        min-width: 0;
        margin: 0;
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
    }
}
