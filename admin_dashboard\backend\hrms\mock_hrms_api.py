from flask import Flask, request, jsonify

app = Flask(__name__)

# Dummy employee data
EMPLOYEES = {
    "1001": {"name": "<PERSON>", "department": "HR", "leave_balance": {"annual": 12, "sick": 5}},
    "1002": {"name": "<PERSON>", "department": "Engineering", "leave_balance": {"annual": 8, "sick": 2}},
}

@app.route('/leave_balance')
def leave_balance():
    employee_id = request.args.get('employee_id')
    leave_type = request.args.get('leave_type', 'annual')
    emp = EMPLOYEES.get(employee_id)
    if not emp:
        return jsonify({"error": "Employee not found"}), 404
    balance = emp["leave_balance"].get(leave_type, 0)
    return jsonify({"employee_id": employee_id, "leave_type": leave_type, "balance": balance})

@app.route('/employee_profile')
def employee_profile():
    employee_id = request.args.get('employee_id')
    emp = EMPLOYEES.get(employee_id)
    if not emp:
        return jsonify({"error": "Employee not found"}), 404
    return jsonify({"employee_id": employee_id, **emp})

# For future: Add endpoints for attendance, payroll, etc.

if __name__ == '__main__':
    app.run(port=5050, debug=True) 