# ZiaHR Admin Dashboard

![Build](https://img.shields.io/github/actions/workflow/status/your-org/your-repo/ci.yml?branch=main)
![License](https://img.shields.io/github/license/your-org/your-repo)

## Overview
A production-grade, enterprise-class admin dashboard for HR chatbot management. Built with React, Vite, Tailwind CSS, shadcn/ui, Zustand, Recharts, tanstack-table, and more. Features advanced analytics, user insights, feedback management, compliance auditing, AI-powered insights, and robust RBAC.

## Tech Stack
- **Framework:** React (Vite)
- **Styling:** Tailwind CSS, shadcn/ui
- **Routing:** react-router-dom
- **State Management:** Zustand
- **Charts:** Recharts
- **Tables:** tanstack-table
- **Notifications:** sonner/react-toastify
- **PDF Export:** react-pdf
- **API:** Axios
- **Maps:** react-simple-maps (for geolocation)
- **CI/CD:** GitHub Actions

## Folder Structure
```
admin_dashboard_ui/
  src/
    components/      # Shared UI components (Sidebar, Layout, etc.)
    hooks/           # Custom hooks (auth, theme, sidebar)
    pages/           # Route-level modules (dashboard, analytics, feedback, ai, etc.)
    services/        # API service (Axios instance)
    types/           # TypeScript types
  public/            # Static assets
  package.json       # Project dependencies
  tailwind.config.js # Tailwind CSS config
  tsconfig.json      # TypeScript config
```

## Setup & Development
1. **Clone the repo:**
   ```bash
   git clone https://github.com/your-org/your-repo.git
   cd admin_dashboard_ui
   ```
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Configure environment:**
   - Copy `.env.sample` to `.env` and set API endpoints, etc.
4. **Run locally:**
   ```bash
   npm run dev
   ```
5. **Build for production:**
   ```bash
   npm run build
   npm run preview
   ```

## Environment Variables
See `.env.sample` for all required variables.

## Deployment
- **Vercel/Netlify:**
  - Connect your repo, set environment variables, and deploy.
  - For static export: `npm run build` (Vite outputs to `dist/`).
- **Self-host:**
  - Serve the `dist/` directory with any static file server.

## API Structure (Summary)
- `/analytics/chats-per-day?range=...`
- `/analytics/top-intents?range=...&department=...`
- `/analytics/topic-trends?range=...&topics=...`
- `/analytics/sentiment-distribution?range=...`
- `/analytics/top-questions?...`
- `/feedback/trends?...`
- `/feedback/escalations?...`
- `/training/misunderstood-queries?...`
- `/training/ner-intent-trainer?...`
- `/escalations/pending`
- `/device/logins?...`
- `/device/sessions?...`
- `/device/geologins`
- `/ai/anomalies?...`
- `/ai/policy-drifts?...`
- `/ai/weekly-digest`
- `/admin/users?...`
- `/admin/users/update-role`
- `/admin/settings`
- `/admin/email-schedule`

## How to Extend
- Add new modules under `src/pages/dashboard/`
- Create reusable components in `src/components/`
- Add new API endpoints in `src/services/api.ts`
- Use Zustand for new global state needs
- Style with Tailwind + shadcn/ui for consistency

## License
[MIT](../LICENSE)
