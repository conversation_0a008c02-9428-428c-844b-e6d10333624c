/* 
 * Theme Styles and Dark Mode
 * Contains dark theme overrides and theme-specific styling
 */

/* Dark Theme Body and Container Styles */
.theme-dark body {
    background-color: #1E1E1E;
}

.theme-dark .chat-container {
    background-color: #1E1E1E;
}

.theme-dark .chat-container::after {
    background-color: #1E1E1E;
}

/* Dark Theme Sidebar Styles */
.theme-dark .sidebar.collapsed .new-chat-btn {
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.theme-dark .sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none;
}

.theme-dark #newChatBtn {
    background-color: transparent !important;
    box-shadow: none;
}

.theme-dark #newChatBtn img.new-chat-icon {
    filter: none;
}

.theme-dark #newChatBtn img.new-chat-icon,
.theme-dark .header-new-chat-btn img.new-chat-icon,
.theme-dark .search-modal .new-chat-icon img {
    content: url('/static/img/new-chat-icon-dark-larger.svg') !important;
}

.theme-dark .sidebar-action-btn:hover {
    background-color: var(--sidebar-item-hover);
}

.theme-dark #newChatBtn:hover {
    background-color: var(--sidebar-item-hover);
    border-color: var(--sidebar-text);
}

.theme-dark .new-chat-btn {
    background-color: #FFFFFF;
    color: #000000;
}

.theme-dark .new-chat-btn:hover {
    box-shadow: 0 2px 5px rgba(255, 255, 255, 0.2);
}

/* Dark Theme Chat History */
.theme-dark .chat-history-item-title-input {
    color: var(--sidebar-text);
    background-color: rgba(10, 16, 33, 0.5);
}

.theme-dark .chat-menu-dropdown {
    background-color: #1E1E1E;
    border-color: #444654;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.theme-dark .chat-menu-item {
    color: #FFFFFF;
}

.theme-dark .chat-menu-item:hover {
    background-color: #343541;
}

.theme-dark .chat-menu-item.rename i,
.theme-dark .chat-menu-item.archive i,
.theme-dark .chat-menu-item.share i,
.theme-dark .chat-menu-item.download i {
    color: #EDEDED;
}

.theme-dark .chat-menu-item.delete {
    color: var(--cta-color);
}

.theme-dark .chat-menu-item.delete i {
    color: var(--cta-color);
}

.theme-dark .chat-menu-item.delete:hover {
    background-color: rgba(255, 107, 107, 0.15);
}

/* Dark Theme Search */
.theme-dark .search-result-snippet .highlight {
    background-color: rgba(138, 112, 214, 0.2);
    color: var(--accent-color);
}

/* Dark Theme Modals */
.theme-dark .modal-content {
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(10, 16, 33, 0.5);
    background-color: var(--bg-primary);
}

.theme-dark .modal-header {
    background-color: #2B2B2B;
    border-bottom: 1px solid #444654;
}

.theme-dark #archivedChatsModal .modal-content {
    background-color: #2B2B2B;
    color: #E0E0E0;
    border: 1px solid #3A3A3A;
}

.theme-dark #archivedChatsModal .modal-header {
    border-bottom: 1px solid #3A3A3A;
}

.theme-dark #archivedChatsModal .modal-footer {
    border-top: 1px solid #3A3A3A;
}

.theme-dark .archived-chat-item {
    border-bottom: 1px solid #3A3A3A;
}

.theme-dark .archived-chat-item:hover {
    background-color: #3A3A3A;
}

.theme-dark .archived-chat-title {
    color: #E0E0E0;
}

.theme-dark .archived-chat-date {
    color: #AAAAAA;
}

.theme-dark .no-archived-chats-message,
.theme-dark .loading-spinner {
    color: #AAAAAA;
}

/* Dark Theme Forms */
.theme-dark .login-message {
    color: #ff6b6b;
}

.theme-dark .dropdown-select {
    background-color: #343541;
    color: #FFFFFF;
    border-color: #444654;
}

.theme-dark .dropdown-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-button {
    background-color: #343541;
    color: #FFFFFF;
    border-color: #444654;
}

.theme-dark .settings-button:hover {
    background-color: #444654;
}

.theme-dark .settings-item-right input[type="text"],
.theme-dark .settings-item-right input[type="email"] {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--input-border) !important;
}

/* Dark Theme Settings */
.theme-dark #settingsModal .modal-content {
    background-color: #1E1E1E;
}

.theme-dark #settingsModal .settings-sidebar {
    background-color: #2B2B2B;
    border-right: 1px solid #444654;
}

.theme-dark .settings-nav-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-nav-item.active {
    background-color: var(--accent-color);
    color: white;
}

.theme-dark .settings-content {
    background-color: #1E1E1E;
    color: var(--text-primary);
}

.theme-dark .settings-panel {
    background-color: #1E1E1E;
}

.theme-dark .settings-item:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

.theme-dark .settings-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-description {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dark Theme Messages */
.theme-dark .bot-message .message-content {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    color: var(--text-primary);
}

.theme-dark .user-message .message-content {
    background-color: rgba(77, 168, 218, 0.15);
    color: var(--text-primary);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-dark .message-content pre {
    background-color: rgba(10, 16, 33, 0.8);
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25), inset 0 1px 2px rgba(79, 139, 249, 0.1);
}

/* Dark Theme Welcome Message */
.theme-dark .greeting-message-container .welcome-message,
.theme-dark #greetingMessageContainer .welcome-message {
    background:
        linear-gradient(145deg, rgba(30, 30, 30, 0.95) 0%, rgba(20, 20, 20, 0.9) 100%),
        radial-gradient(circle at 20% 20%, rgba(147, 197, 253, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(196, 181, 253, 0.03) 0%, transparent 50%);
}

.theme-dark .greeting-message-container .welcome-message::before,
.theme-dark #greetingMessageContainer .welcome-message::before {
    background:
        radial-gradient(circle at 30% 30%, rgba(147, 197, 253, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(196, 181, 253, 0.04) 0%, transparent 50%);
}

.theme-dark .greeting-message-container .welcome-message:hover,
.theme-dark #greetingMessageContainer .welcome-message:hover {
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 12px 20px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Dark Theme Input */
.theme-dark .chat-input-container {
    background-color: var(--input-bg);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .input-main-area,
.theme-dark .input-actions-row {
    background-color: var(--input-bg);
}

.theme-dark .input-wrapper {
    box-shadow: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .input-wrapper:focus-within {
    box-shadow: none;
    border-color: rgba(255, 255, 255, 0.2);
}

.theme-dark .input-document {
    border: 1px solid rgba(79, 139, 249, 0.4);
    background-color: rgba(22, 32, 66, 0.8);
    box-shadow: none;
}

.theme-dark .document-icon {
    background-color: #666666;
    box-shadow: none;
}

/* Dark Theme Suggestions */
.theme-dark .input-suggestions .suggestion-chip {
    background-color: var(--bg-primary, #1a1a1a);
    color: var(--text-primary, #ffffff);
    border-color: var(--border-color, #404040);
}

.theme-dark .input-suggestions .suggestion-chip:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: var(--text-secondary, #cccccc) !important;
}

/* Dark Theme Share */
.theme-dark .share-link-input {
    background-color: rgba(22, 32, 66, 0.8);
    border-color: rgba(79, 139, 249, 0.3);
    color: #f8fafc;
}

.theme-dark .share-option {
    background-color: rgba(22, 32, 66, 0.8);
    border-color: rgba(79, 139, 249, 0.3);
}

.theme-dark .share-option:hover {
    background-color: #4f8bf9;
    border-color: #4f8bf9;
}

.theme-dark .archived-indicator {
    color: rgba(209, 216, 230, 0.7);
}

/* Dark Theme User Dropdown */
.theme-dark .user-dropdown-item.logout {
    color: #ff6b6b;
}

/* Dark Theme Voice/Recording */
.theme-dark .recording-waves span {
    background-color: #ffffff;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 5px rgba(255, 255, 255, 0.4);
}

.theme-dark .transcription-result {
    background-color: rgba(22, 32, 66, 0.8);
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-dark .transcription-result:focus {
    border-color: rgba(79, 139, 249, 0.7);
    box-shadow: 0 0 0 2px rgba(79, 139, 249, 0.25);
}

/* Dark Theme Notifications */
.theme-dark .confirmation-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

.theme-dark .simple-toast {
    background-color: #4a5568;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.theme-dark .simple-toast.success {
    background-color: #4CAF50;
    border-left: 4px solid #388E3C;
}

.theme-dark .simple-toast.error {
    background-color: #F44336;
    border-left: 4px solid #D32F2F;
}

.theme-dark .simple-toast.info {
    background-color: #343541;
    border-left: 4px solid #444654;
}

/* Dark Theme Escalation */
.theme-dark #inlineEscalationIconContainer i {
    color: #FF7F50;
}
