/* 
 * Layout and Container Styles
 * Contains main application layout, containers, and structural elements
 */

/* Main Application Container */
.app-container {
    display: flex;
    flex-direction: row;
    width: 100vw;
    max-width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
    background-color: var(--bg-primary);
    position: relative;
}

/* Ensure chat input container stays centered in the app container */
.app-container .chat-input-container {
    left: 50%; /* Keep this for initial centering */
    transform: translateX(-50%);
    position: absolute;
    bottom: 24px;
    z-index: 100;
}

/* After welcome message is removed, only center horizontally */
body.welcome-removed .chat-input-container {
    transform: translate(-50%, 0) !important; /* Center horizontally only */
    bottom: 24px !important;
    position: fixed !important;
}

/* Hidden sidebar state (for backward compatibility) */
.sidebar.hidden {
    display: none;
}

.app-container.sidebar-hidden {
    width: 100%;
}

/* Chat Messages Container */
.chat-messages {
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    padding: 0;
    margin: 0;
    height: calc(100vh - var(--footer-height));
}

/* Sidebar layout */
.sidebar {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.sidebar-conversations {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Greeting/Welcome Card Layout */
.greeting-message-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 20px;
    text-align: center;
    position: static !important;
    margin-bottom: 8px !important;
}

/* Ensure greeting message container is visible for logged-in users when chat is empty */
#greetingMessageContainer {
    display: block !important;
    text-align: center;
    margin: 0 auto;
}

/* Hide greeting message container only when chat has messages */
.chat-messages.has-messages #greetingMessageContainer {
    display: none !important;
}

/* Ensure the greeting message is visible when there are no messages */
#chatMessages:empty .greeting-message-container,
#chatMessages:not(:has(.user-message, .bot-message)) .greeting-message-container {
    display: block !important;
    text-align: center;
}

/* Chat input positioning and layout */
.chat-input-container {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;
    max-width: 100vw;
    z-index: 1000;
    padding: 4px 24px !important;
    max-width: 560px !important;
    min-width: 340px !important;
    flex-shrink: 0;
}

/* Sidebar-specific layout adjustments */
.sidebar:not(.collapsed) ~ .chat-container .chat-input-container {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    bottom: auto !important;
    width: 93.5% !important;
    max-width: 600px !important;
    min-width: 340px !important;
}

/* Responsive layout adjustments */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .chat-input-container {
        position: fixed !important;
        bottom: 16px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: calc(100vw - 32px) !important;
        max-width: calc(100vw - 32px) !important;
        min-width: auto !important;
        padding: 0 16px !important;
    }
}
