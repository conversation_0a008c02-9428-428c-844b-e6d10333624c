/* 
 * CSS Custom Properties and Theme Variables
 * Contains all CSS variables used throughout the application
 */

/* ChatGPT-style CSS - ZiaHR Color Palette */
:root {
    /* Light Theme (Default) - Black, White, Gray Theme */
    --bg-primary: #FFFFFF; /* Pure white for main chat panel */
    --bg-secondary: #F8F8F8; /* Light gray for secondary elements */
    --text-primary: #000000; /* Black for maximum readability */
    --text-secondary: #666666; /* Medium gray for de-emphasis */
    --accent-color: #333333; /* Dark gray accent color */
    --accent-color-rgb: 51, 51, 51; /* RGB values for accent color */
    --accent-hover: #555555; /* Medium gray on hover */
    --border-color: #DADADA; /* Light gray border color */
    --message-user-bg: #F2F2F2; /* Light gray for user messages */
    --message-bot-bg: #FFFFFF; /* White for bot messages */
    --message-text: #000000; /* Black for message text */
    --shadow-color: rgba(0, 0, 0, 0.06); /* Subtle shadow */
    --input-bg: #FFFFFF; /* Pure white for input background */
    --input-border: #DADADA; /* Light gray border for input */
    --input-text: #000000; /* Black for input text */
    --input-placeholder: #999999; /* Medium gray for placeholder */
    --welcome-bg: #FFFFFF; /* Match main background */
    --chip-bg: #F2F2F2; /* Light gray for suggestion chips */
    --chip-text: #000000; /* Black for chip text */
    --chip-hover-bg: #333333; /* Dark gray for chip hover */
    --chip-hover-text: #FFFFFF; /* White text on hover */
    --button-disabled: #E5E5E5; /* Light gray for disabled buttons */
    --cta-color: #333333; /* Dark gray for CTAs */
    --cta-hover: #555555; /* Medium gray for CTA hover */
    --success-color: #444444; /* Dark gray for success messages */
    --error-color: #666666; /* Medium gray for errors */
    --header-height: 60px;
    --footer-height: 100px;
    --resizer-width: 5px;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.06); /* Shadow for card-like elements */

    /* ChatGPT-specific variables */
    --chatgpt-message-spacing: 30px;
    --chatgpt-message-padding: 16px;
}

/* Dark Theme - Black, White, Gray Theme */
.theme-dark {
    --bg-primary: #1E1E1E; /* Dark background for main chat area */
    --bg-secondary: #2B2B2B; /* Slightly lighter for secondary elements */
    --text-primary: #FFFFFF; /* White text for dark mode */
    --text-secondary: #EDEDED; /* Light gray secondary text */
    --accent-color: #999999; /* Medium gray accent for dark mode */
    --accent-color-rgb: 153, 153, 153; /* RGB values for accent color */
    --accent-hover: #AAAAAA; /* Lighter gray on hover */
    --border-color: #333333; /* Dark gray border color */
    --message-user-bg: rgba(255, 255, 255, 0.1); /* Subtle white for user messages */
    --message-bot-bg: rgba(255, 255, 255, 0.05); /* Very subtle white for bot messages */
    --message-text: #FFFFFF; /* Match primary text */
    --shadow-color: rgba(0, 0, 0, 0.3); /* Deeper shadows */
    --sidebar-bg: #2c2c2c; /* Dark gray for dark theme */
    --sidebar-text: #e5e5e5; /* Light text for dark background */
    --sidebar-hover: #3a3a3a; /* Slightly lighter gray for hover */
    --sidebar-border: #404040; /* Dark border */
    --sidebar-item-hover: rgba(255, 255, 255, 0.1); /* Subtle light hover background */
    --sidebar-item-active: #404040; /* Dark gray for active items */
    --sidebar-accent: #e5e5e5; /* Light accent color */
    --input-bg: #343541; /* Dark gray for input background */
    --input-border: #444654; /* Slightly lighter border for input */
    --input-text: #FFFFFF; /* White text */
    --input-placeholder: #BBBBBB; /* Light gray placeholder */
    --welcome-bg: #1E1E1E; /* Match main background */
    --chip-bg: rgba(255, 255, 255, 0.1); /* Subtle white for chips */
    --chip-text: #FFFFFF; /* White text */
    --chip-hover-bg: #666666; /* Medium gray for hover */
    --chip-hover-text: #FFFFFF; /* Pure white on hover */
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Stronger shadow for dark mode */
    --cta-color: #999999; /* Medium gray for CTAs */
    --cta-hover: #AAAAAA; /* Lighter gray for hover */
    --success-color: #AAAAAA; /* Light gray for success */
    --error-color: #777777; /* Medium gray for errors */
}
