{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "open": "Open", "yes": "Yes", "no": "No", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "all": "All", "none": "None", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "online": "Online", "offline": "Offline", "pending": "Pending", "completed": "Completed", "failed": "Failed", "processing": "Processing"}, "navigation": {"dashboard": "Dashboard", "analytics": "Analytics", "overview": "Overview", "insights": "Insights", "chat_logs": "Chat Logs", "feedback": "Feedback & Escalations", "trends": "Trends", "escalated": "Escalated", "resolution": "Resolution", "ai_insights": "AI Insights", "weekly_digest": "Weekly Digest", "policy_drift": "Policy Drift", "training_tools": "Training Tools", "misunderstood": "Misunderstood Queries", "ner_intent": "NER & Intent", "live_support": "Live Support", "queue": "Queue", "ongoing": "Ongoing", "compliance": "Compliance & Auditing", "gdpr": "GDPR", "deletion": "Data Deletion", "sensitive": "Sensitive Data", "users": "Users", "admins": "Admins", "roles": "Roles", "settings": "Settings", "theme": "Theme", "email": "Email", "device_intelligence": "Device Intelligence", "feature_requests": "Feature Requests", "api_management": "API Management", "webhooks": "Webhooks", "system_health": "System Health"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "otp": "One-Time Password", "two_factor": "Two-Factor Authentication", "backup_codes": "Backup Codes", "remember_me": "Remember me", "forgot_password": "Forgot password?", "reset_password": "Reset Password", "change_password": "Change Password", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "enable_2fa": "Enable Two-Factor Authentication", "disable_2fa": "Disable Two-Factor Authentication", "scan_qr_code": "Scan QR code with your authenticator app", "enter_verification_code": "Enter verification code", "invalid_credentials": "Invalid credentials", "session_expired": "Session expired. Please login again.", "access_denied": "Access denied", "login_success": "Login successful", "logout_success": "Logout successful", "password_changed": "Password changed successfully", "2fa_enabled": "Two-factor authentication enabled", "2fa_disabled": "Two-factor authentication disabled"}, "dashboard": {"title": "ZiaHR Admin Dashboard", "welcome": "Welcome back", "overview": "Overview", "quick_stats": "Quick Stats", "recent_activity": "Recent Activity", "system_status": "System Status", "alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications", "total_users": "Total Users", "active_sessions": "Active Sessions", "total_queries": "Total Queries", "avg_response_time": "Avg Response Time", "user_satisfaction": "User Satisfaction", "escalation_rate": "Escalation Rate", "resolution_rate": "Resolution Rate", "uptime": "Uptime", "last_updated": "Last updated", "refresh_data": "Refresh Data", "view_details": "View Details", "no_data": "No data available", "loading_metrics": "Loading metrics...", "error_loading": "Error loading data"}, "metrics": {"chatbot_performance": "<PERSON><PERSON><PERSON>", "user_engagement": "User Engagement", "sentiment_analysis": "Sentiment Analysis", "intent_recognition": "Intent Recognition", "response_times": "Response Times", "error_rates": "Error Rates", "anomalies_detected": "Anomalies Detected", "performance_trends": "Performance Trends", "daily_queries": "Daily Queries", "weekly_summary": "Weekly Summary", "monthly_report": "Monthly Report", "real_time_metrics": "Real-time Metrics", "historical_data": "Historical Data", "comparison": "Comparison", "benchmark": "Benchmark", "target": "Target", "actual": "Actual", "variance": "<PERSON><PERSON><PERSON>", "improvement": "Improvement", "decline": "Decline"}, "chat": {"sessions": "Chat <PERSON>", "messages": "Messages", "duration": "Duration", "satisfaction": "Satisfaction", "escalate": "Escalate", "resolve": "Resolve", "add_note": "Add Note", "view_transcript": "View Transcript", "user_info": "User Information", "session_details": "Session Details", "escalation_reason": "Escalation Reason", "resolution_notes": "Resolution Notes", "chat_history": "Chat History", "search_conversations": "Search Conversations", "filter_by_date": "Filter by Date", "filter_by_status": "Filter by Status", "export_transcript": "Export Transcript", "escalated_chats": "Escalated Chats", "resolved_chats": "Resolved Chats", "ongoing_chats": "Ongoing Chats", "average_duration": "Average Duration", "total_messages": "Total Messages"}, "users": {"user_management": "User Management", "admin_users": "Admin Users", "create_user": "Create User", "edit_user": "Edit User", "delete_user": "Delete User", "invite_user": "Invite User", "user_details": "User Details", "full_name": "Full Name", "email_address": "Email Address", "role": "Role", "organization": "Organization", "last_login": "Last Login", "created_at": "Created At", "updated_at": "Updated At", "user_status": "User Status", "permissions": "Permissions", "change_role": "Change Role", "reset_password": "Reset Password", "send_invitation": "Send Invitation", "revoke_access": "Revoke Access", "user_activity": "User Activity", "login_history": "Login History", "device_info": "Device Information", "security_settings": "Security Settings"}, "roles": {"superadmin": "Super Admin", "admin": "Admin", "support_agent": "Support Agent", "compliance_auditor": "Compliance Auditor", "developer": "Developer", "hr_lead": "HR Lead", "viewer": "Viewer", "role_description": "Role Description", "permissions_included": "Permissions Included", "manage_roles": "Manage Roles", "create_role": "Create Role", "edit_role": "Edit Role", "delete_role": "Delete Role", "assign_role": "Assign Role", "role_hierarchy": "Role Hierarchy", "inherited_permissions": "Inherited Permissions"}, "audit": {"audit_logs": "<PERSON><PERSON>", "device_logs": "<PERSON><PERSON> Logs", "session_logs": "Session Logs", "activity_timeline": "Activity Timeline", "security_events": "Security Events", "compliance_report": "Compliance Report", "data_access": "Data Access", "user_actions": "User Actions", "system_changes": "System Changes", "login_attempts": "Login Attempts", "failed_logins": "Failed <PERSON><PERSON>", "suspicious_activity": "Suspicious Activity", "risk_assessment": "Risk Assessment", "compliance_status": "Compliance Status", "audit_trail": "Audit Trail", "export_logs": "Export Logs", "filter_logs": "Filter Logs", "search_logs": "Search Logs"}, "feedback": {"feature_requests": "Feature Requests", "submit_request": "Submit Request", "request_title": "Request Title", "request_description": "Request Description", "priority": "Priority", "category": "Category", "status": "Status", "votes": "Votes", "comments": "Comments", "add_comment": "Add Comment", "vote_request": "Vote for Request", "implementation_status": "Implementation Status", "estimated_effort": "Estimated <PERSON><PERSON><PERSON>", "target_release": "Target Release", "feedback_trends": "Feedback Trends", "user_satisfaction": "User Satisfaction", "improvement_suggestions": "Improvement Suggestions", "bug_reports": "Bug Reports", "enhancement_requests": "Enhancement Requests"}, "integrations": {"api_keys": "API Keys", "webhooks": "Webhooks", "create_api_key": "Create API Key", "create_webhook": "Create Webhook", "api_key_name": "API Key Name", "webhook_url": "Webhook URL", "webhook_events": "Webhook Events", "webhook_secret": "Webhook Secret", "test_webhook": "Test Webhook", "webhook_deliveries": "Webhook Deliveries", "api_usage": "API Usage", "rate_limits": "Rate Limits", "integration_status": "Integration Status", "external_services": "External Services", "third_party_apps": "Third-party Apps", "oauth_applications": "OAuth Applications", "api_documentation": "API Documentation"}, "system": {"system_health": "System Health", "service_status": "Service Status", "performance_metrics": "Performance Metrics", "error_monitoring": "Error Monitoring", "uptime_monitoring": "Uptime Monitoring", "resource_usage": "Resource Usage", "database_health": "Database Health", "cache_status": "<PERSON>ache <PERSON>", "queue_status": "Queue Status", "background_jobs": "Background Jobs", "system_logs": "System Logs", "maintenance_mode": "Maintenance Mode", "backup_status": "Backup Status", "security_scan": "Security Scan", "vulnerability_report": "Vulnerability Report", "system_updates": "System Updates"}, "export": {"export_data": "Export Data", "export_format": "Export Format", "date_range": "Date Range", "select_fields": "Select Fields", "download_export": "Download Export", "export_status": "Export Status", "export_history": "Export History", "scheduled_exports": "Scheduled Exports", "create_schedule": "Create Schedule", "export_frequency": "Export Frequency", "export_recipients": "Export Recipients", "file_size": "File Size", "export_progress": "Export Progress", "export_completed": "Export Completed", "export_failed": "Export Failed"}, "settings": {"general_settings": "General Settings", "security_settings": "Security Settings", "notification_settings": "Notification Settings", "appearance": "Appearance", "language": "Language", "timezone": "Timezone", "date_format": "Date Format", "time_format": "Time Format", "theme_mode": "Theme Mode", "light_mode": "Light Mode", "dark_mode": "Dark Mode", "system_mode": "System Mode", "email_notifications": "Email Notifications", "push_notifications": "Push Notifications", "security_alerts": "Security Alerts", "system_updates": "System Updates", "privacy_settings": "Privacy Settings", "data_retention": "Data Retention", "cookie_preferences": "Cookie Preferences"}, "errors": {"generic_error": "An error occurred. Please try again.", "network_error": "Network error. Please check your connection.", "server_error": "Server error. Please try again later.", "validation_error": "Please check your input and try again.", "permission_denied": "You don't have permission to perform this action.", "not_found": "The requested resource was not found.", "timeout_error": "Request timeout. Please try again.", "rate_limit_exceeded": "Rate limit exceeded. Please try again later.", "maintenance_mode": "System is under maintenance. Please try again later.", "session_expired": "Your session has expired. Please login again.", "invalid_token": "Invalid or expired token.", "file_too_large": "File size exceeds the maximum limit.", "unsupported_format": "Unsupported file format.", "quota_exceeded": "<PERSON><PERSON><PERSON> exceeded. Please upgrade your plan.", "error_loading": "Error loading data"}, "success": {"data_saved": "Data saved successfully", "user_created": "User created successfully", "user_updated": "User updated successfully", "user_deleted": "User deleted successfully", "role_changed": "Role changed successfully", "settings_updated": "Settings updated successfully", "export_completed": "Export completed successfully", "import_completed": "Import completed successfully", "email_sent": "<PERSON>ail sent successfully", "notification_sent": "Notification sent successfully", "backup_created": "Backup created successfully", "cache_cleared": "<PERSON><PERSON> cleared successfully", "system_updated": "System updated successfully"}}