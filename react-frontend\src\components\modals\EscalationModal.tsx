import React, { useState } from 'react';
import { User, EscalationForm } from '@/types';

interface EscalationModalProps {
  onClose: () => void;
  onSubmit: (formData: EscalationForm) => void;
  user: User | null;
}

const EscalationModal: React.FC<EscalationModalProps> = ({ onClose, onSubmit, user }) => {
  const [formData, setFormData] = useState<EscalationForm>({
    hrPerson: '',
    issueType: 'policy',
    issueDescription: '',
    priority: 'medium',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const hrPersons = [
    { value: 'sarah.johnson', label: '<PERSON> - <PERSON>R <PERSON>' },
    { value: 'mike.chen', label: '<PERSON>' },
    { value: 'lisa.rodriguez', label: '<PERSON> Coordinator' },
    { value: 'david.kim', label: '<PERSON>' },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.hrPerson || !formData.issueDescription.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      onSubmit(formData);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content max-w-lg">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-primary-border dark:border-dark-border">
          <h2 className="text-lg font-semibold text-primary-text dark:text-dark-text">
            Escalate Issue to HR
          </h2>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-primary-text-secondary dark:text-dark-text-secondary"></i>
          </button>
        </div>

        {/* Body */}
        <div className="p-4">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* User Details */}
            <div className="form-group">
              <label className="form-label">Your Details:</label>
              <div className="p-3 bg-primary-secondary dark:bg-dark-secondary rounded border border-primary-border dark:border-dark-border text-sm">
                <div className="text-primary-text dark:text-dark-text font-medium">
                  {user?.fullName || 'User Name'}
                </div>
                <div className="text-primary-text-secondary dark:text-dark-text-secondary">
                  {user?.email || '<EMAIL>'}
                </div>
                {user?.employeeId && (
                  <div className="text-primary-text-secondary dark:text-dark-text-secondary">
                    Employee ID: {user.employeeId}
                  </div>
                )}
              </div>
            </div>

            {/* HR Person */}
            <div className="form-group">
              <label htmlFor="hrPerson" className="form-label">
                Select HR Representative:
              </label>
              <select
                id="hrPerson"
                name="hrPerson"
                value={formData.hrPerson}
                onChange={handleInputChange}
                className="form-input"
                required
              >
                <option value="">Select HR Representative</option>
                {hrPersons.map((person) => (
                  <option key={person.value} value={person.value}>
                    {person.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Issue Type */}
            <div className="form-group">
              <label htmlFor="issueType" className="form-label">
                Issue Type:
              </label>
              <select
                id="issueType"
                name="issueType"
                value={formData.issueType}
                onChange={handleInputChange}
                className="form-input"
                required
              >
                <option value="policy">Policy Related</option>
                <option value="benefits">Benefits</option>
                <option value="workplace">Workplace Issue</option>
                <option value="compensation">Compensation</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Issue Description */}
            <div className="form-group">
              <label htmlFor="issueDescription" className="form-label">
                Issue Description:
              </label>
              <textarea
                id="issueDescription"
                name="issueDescription"
                value={formData.issueDescription}
                onChange={handleInputChange}
                rows={3}
                className="form-input resize-none"
                placeholder="Describe your issue..."
                required
              />
            </div>

            {/* Priority */}
            <div className="form-group">
              <label htmlFor="priority" className="form-label">
                Priority Level:
              </label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="form-input"
                required
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            {/* Form Actions */}
            <div className="flex justify-center space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isSubmitting || !formData.hrPerson || !formData.issueDescription.trim()}
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="loading-spinner"></div>
                    <span>Submitting...</span>
                  </div>
                ) : (
                  'Submit Escalation'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EscalationModal;
