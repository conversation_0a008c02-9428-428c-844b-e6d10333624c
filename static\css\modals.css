/* 
 * Modal and Dialog Styles
 * Contains styles for modals, dialogs, overlays, and popup components
 */

/* Base Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    margin: 5% auto;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-lg {
    max-width: 700px;
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 18px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Modal Body */
.modal-body {
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
    flex: 1;
    background-color: var(--bg-primary);
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 18px;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-primary);
}

.modal-footer-left {
    text-align: left;
}

.modal-footer-right {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 14px;
}

.modal-footer a:hover {
    text-decoration: underline;
}

/* Modern Login Modal */
.modern-login {
    max-width: 340px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

.modern-login .modal-header {
    padding: 10px 16px;
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

.modern-login .modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.modern-login .modal-body {
    padding: 12px 16px;
}

/* Settings Modal */
#settingsModal .modal-content {
    width: 90%;
    max-width: 588px;
    height: 455px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Voice Modal */
#voiceModal .modal-content {
    background: rgba(255,255,255,0.85);
    backdrop-filter: blur(8px);
    border-radius: 18px;
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    max-width: 420px;
    width: 90%;
}

#voiceModal .modal-header {
    background: transparent;
    border-bottom: 1px solid #e3eafc;
    padding: 18px 24px 10px 24px;
}

#voiceModal .modal-body {
    padding: 24px 24px 12px 24px;
    background: transparent;
    display: block;
    max-height: 300px;
    overflow-y: auto;
}

#voiceModal .modal-footer {
    border-top: 1px solid #e3eafc;
    background: transparent;
    padding: 14px 24px 18px 24px;
}

/* Archived Chats Modal */
#archivedChatsModal .modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
}

#archivedChatsModal .modal-body {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
}

/* Dropdown Styles */
.dropdown-select {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    min-width: 120px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
}

.dropdown-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

.dropdown-select option {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dropdown-select i {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Chat Menu Dropdown */
.chat-menu-dropdown {
    position: absolute;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 160px;
    padding: 4px 0;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.chat-menu-dropdown.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.chat-menu-item {
    padding: 6px 14px;
    cursor: pointer;
    font-size: 13px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease;
}

.chat-menu-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.chat-menu-item i {
    width: 16px;
    text-align: center;
    margin-right: 8px;
    font-size: 12px;
}

.chat-menu-item.delete {
    color: var(--error-color);
}

.chat-menu-item.delete i {
    color: var(--error-color);
}

.chat-menu-item.delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Dark Theme Modal Styles */
.theme-dark .modal-content {
    background-color: #1E1E1E;
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(10, 16, 33, 0.5);
}

.theme-dark .modal-header {
    background-color: #2B2B2B;
    border-bottom: 1px solid #444654;
}

.theme-dark #archivedChatsModal .modal-content {
    background-color: #2B2B2B;
    color: #E0E0E0;
    border: 1px solid #3A3A3A;
}

.theme-dark #archivedChatsModal .modal-header {
    border-bottom: 1px solid #3A3A3A;
}

.theme-dark #archivedChatsModal .modal-footer {
    border-top: 1px solid #3A3A3A;
}

.theme-dark .dropdown-select {
    background-color: #343541;
    color: #FFFFFF;
    border-color: #444654;
}

.theme-dark .dropdown-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.theme-dark .chat-menu-dropdown {
    background-color: #1E1E1E;
    border-color: #444654;
}

.theme-dark .chat-menu-item {
    color: #FFFFFF;
}

.theme-dark .chat-menu-item:hover {
    background-color: #343541;
}

.theme-dark .chat-menu-item.delete {
    color: var(--cta-color);
}

.theme-dark .chat-menu-item.delete i {
    color: var(--cta-color);
}

.theme-dark .chat-menu-item.delete:hover {
    background-color: rgba(255, 107, 107, 0.15);
}

/* Responsive Modal Adjustments */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }

    #settingsModal .modal-content {
        width: 95%;
        max-width: none;
        height: 90vh;
        max-height: 90vh;
    }

    .dropdown-select {
        font-size: 11px;
        min-width: 90px;
    }
}

@media (max-width: 480px) {
    .modal-content, .modal-lg {
        max-width: 100vw !important;
        width: 100vw !important;
        min-width: 0;
        margin: 0;
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
    }
}
