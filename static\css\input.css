/* 
 * Input and Form Controls
 * Contains styles for chat input, form elements, and input-related components
 */

/* Chat Input Container */
.chat-input-container {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;
    max-width: 100vw;
    z-index: 1000;
    padding: 4px 24px !important;
    max-width: 560px !important;
    min-width: 340px !important;
    flex-shrink: 0;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Chat Input Form */
.chat-input-form {
    width: 100% !important;
    max-width: 648px !important;
    min-width: 0;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ChatGPT Input Wrapper */
.chatgpt-input-wrapper {
    display: flex !important;
    flex-direction: column !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 26px !important;
    padding: 16px !important;
    gap: 12px !important;
    min-height: 80px !important;
    width: 100% !important;
    position: relative !important;
    overflow: visible !important;
}

/* Enhanced focus state with subtle glow when text is present */
.chatgpt-input-wrapper.has-text:focus-within {
    border-color: transparent !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Normal focus state when no text */
.chatgpt-input-wrapper:focus-within {
    border-color: #9ca3af !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Main Input Area - Top Section */
.chatgpt-input-area {
    width: 116% !important;
    order: 1 !important;
}

.chatgpt-input-area textarea {
    width: 116% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    resize: none !important;
    font-size: 16px !important;
    line-height: 24px !important;
    color: #374151 !important;
    font-family: inherit !important;
    padding: 0 !important;
    margin: 0 !important;
    min-height: 24px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.chatgpt-input-area textarea::-webkit-scrollbar {
    display: none !important;
}

.chatgpt-input-area textarea::placeholder {
    color: #9ca3af !important;
    font-size: 16px !important;
}

/* Bottom Tools Section */
.chatgpt-bottom-tools {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    order: 2 !important;
    width: 100% !important;
}

.chatgpt-left-tools {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.chatgpt-right-tools {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.chatgpt-tool-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    background: transparent !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    color: #6b7280 !important;
}

.chatgpt-tool-btn:hover {
    background: #f7f7f8 !important;
    box-shadow: none !important;
}

.chatgpt-tool-btn:active, .chatgpt-tool-btn.selected {
    background: #f3f4f6 !important;
    box-shadow: none !important;
}

.chatgpt-tool-btn i {
    margin: auto !important;
    display: block !important;
}

.chatgpt-tool-btn:hover {
    background-color: #f3f4f6 !important;
    color: #374151 !important;
}

.chatgpt-tool-btn:active {
    background-color: #e5e7eb !important;
}

.chatgpt-tool-btn i {
    font-size: 14px !important;
}

.chatgpt-tool-btn span {
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-left: 6px !important;
}

/* Send Button - Bottom Right */
.chatgpt-send-btn {
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    border-radius: 8px !important;
    background: #2563eb !important;
    color: white !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
}

.chatgpt-send-btn:hover:not(:disabled) {
    background: #1a1a1a !important;
}

.chatgpt-send-btn:disabled {
    background: #d1d5db !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
}

.chatgpt-send-btn svg {
    width: 16px !important;
    height: 16px !important;
}

/* Input Actions */
.input-actions {
    display: flex;
    align-items: center;
    padding-right: 8px;
    gap: 8px;
}

.input-action-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.input-action-btn:hover {
    color: var(--accent-color);
}

/* Input Controls */
.chat-input-controls {
    display: flex;
    align-items: center;
}

.chat-input-controls #sendBtn {
    margin-left: 5px;
}

/* Input Wrapper */
.input-wrapper {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
}

.input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: none;
}

/* Input Actions Row */
.input-actions-row {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    min-width: 0;
    box-sizing: border-box;
}

.input-actions-left {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-end !important;
    gap: 8px !important;
}

/* Input Document Styles */
.input-document {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 4px 0;
    font-size: 14px;
    color: var(--text-primary);
}

/* Input Suggestions */
.input-suggestions {
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px !important;
    justify-content: center;
    align-items: center;
}

.input-suggestions .suggestion-chip {
    background-color: var(--bg-primary, #FFFFFF);
    color: var(--text-primary, #000000);
    border: 1px solid var(--border-color, #E5E5E5);
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-suggestions .suggestion-chip:hover {
    background-color: rgba(0, 0, 0, 0.08) !important;
    border-color: var(--text-secondary, #666666) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.input-suggestions .suggestion-chip:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
    background-color: rgba(0, 0, 0, 0.12) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Placeholder Styles */
textarea::placeholder, .chatgpt-input-area textarea::placeholder {
    color: var(--input-placeholder);
    font-family: 'Inter', 'Segoe UI', 'Roboto', Arial, sans-serif;
    font-size: 0.6rem;
    font-weight: 400;
    opacity: 0.8;
}

/* Input Footer (Hidden) */
.input-footer {
    display: none;
}
