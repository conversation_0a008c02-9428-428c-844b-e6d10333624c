import re
from typing import List
from .base import Chunking<PERSON>trategy, ChunkingConfig, TokenizerManager
import numpy as np
import random

class TokenAwareStrategy(ChunkingStrategy):
    # ... (full class code from text_chunker.py)
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not config.use_tokens:
            return self._fallback_char_chunking(text, config)

        # Adaptive chunk sizing
        max_tokens = config.max_tokens
        if getattr(config, 'adaptive', False):
            sentences = self._split_into_sentences(text)
            avg_len = np.mean([tokenizer_manager.count_tokens(s, config.tokenizer_name) for s in sentences])
            # Example: scale max_tokens up for longer sentences, down for shorter
            if avg_len > 40:
                max_tokens = min(1024, int(avg_len * 1.5))
            elif avg_len < 15:
                max_tokens = max(256, int(avg_len * 2))
            # else leave as is
        else:
            sentences = self._split_into_sentences(text)

        chunks = []
        current_chunk = ""
        for sentence in sentences:
            test_chunk = self._join_with_space(current_chunk, sentence)
            token_count = tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name)
            if token_count <= max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                if tokenizer_manager.count_tokens(sentence, config.tokenizer_name) > max_tokens:
                    chunks.extend(self._split_oversized_sentence(sentence, config, tokenizer_manager))
                else:
                    current_chunk = sentence
        if current_chunk:
            chunks.append(current_chunk.strip())

        # Temperature-controlled overlap
        if getattr(config, 'temperature_overlap', False):
            # Example: vary overlap based on sentence length or randomly
            overlapped_chunks = [chunks[0]] if chunks else []
            for i in range(1, len(chunks)):
                prev_chunk = chunks[i-1]
                current_chunk = chunks[i]
                # Example: overlap more if previous chunk is long, less if short
                prev_len = tokenizer_manager.count_tokens(prev_chunk, config.tokenizer_name)
                if prev_len > max_tokens * 0.8:
                    overlap_tokens = int(max_tokens * 0.2)
                elif prev_len < max_tokens * 0.5:
                    overlap_tokens = int(max_tokens * 0.05)
                else:
                    overlap_tokens = int(max_tokens * 0.1)
                # Or: overlap_tokens = random.randint(0, int(max_tokens * 0.2))
                overlap = self._get_token_overlap(prev_chunk, overlap_tokens, config, tokenizer_manager)
                if overlap and not current_chunk.startswith(overlap):
                    overlapped_chunks.append(f"{overlap} {current_chunk}")
                else:
                    overlapped_chunks.append(current_chunk)
            return overlapped_chunks
        else:
            return self._add_token_overlap(chunks, config, tokenizer_manager)
    pass 