import React from "react";
import { Sidebar } from "./Sidebar";
import { Outlet } from "react-router-dom";
import { useSidebarStore } from "../hooks/useSidebarStore";
import { useThemeStore } from "../hooks/useThemeStore";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sun, Moon } from "lucide-react";
import { UserProfile } from "./UserProfile";
import { GlobalDateFilter } from "./GlobalDateFilter";

export const Layout = () => {
  const { sidebarOpen } = useSidebarStore();
  const { theme, toggleTheme } = useThemeStore();

  return (
    <div className="min-h-screen flex bg-background text-foreground">
      <Sidebar />
      <div className={`flex-1 flex flex-col transition-all duration-300 ${sidebarOpen ? 'ml-[299px]' : 'ml-[74px]'}`}>
        {/* Header */}
        <header className="sticky top-0 z-30 flex items-center justify-between h-16 px-6 bg-background border-b border-border shadow-sm">
          <div className="flex items-center gap-4">
            <div className="font-bold text-xl tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ZiaHR Admin Dashboard
            </div>
            {/* Removed GlobalDateFilter (date range picker) */}
          </div>
          
          <div className="flex items-center gap-3">
            {/* Live Indicator */}
            <div className="hidden lg:flex items-center gap-2 px-3 py-1.5 bg-green-50 dark:bg-green-900/20 rounded-full">
              <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
              <span className="text-xs font-medium text-green-700 dark:text-green-400">
                Live Data
              </span>
            </div>
            
            {/* Theme Toggle */}
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleTheme} 
              aria-label="Toggle theme"
              className="h-9 w-9"
            >
              {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>
            
            {/* User Profile */}
            <UserProfile />
          </div>
        </header>
        
        {/* Main Content */}
        <main className="flex-1 p-6 overflow-y-auto bg-muted/30">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
}; 