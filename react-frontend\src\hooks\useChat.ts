import { useState, useEffect, useCallback } from 'react';
import { Message, ChatSession, FileAttachment } from '@/types';
import { chatAPI } from "@/utils/api";

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<FileAttachment[]>([]);

  useEffect(() => {
    // Load chat history and current session from localStorage
    loadChatHistory();
    const savedSessionId = localStorage.getItem('currentSessionId');
    if (savedSessionId === 'null' || savedSessionId === null) {
      setCurrentSessionId(null);
    } else if (savedSessionId) {
      setCurrentSessionId(savedSessionId);
    }
  }, []);

  // Ensure messages are loaded for the selected session after both chatSessions and currentSessionId are available
  useEffect(() => {
    if (currentSessionId && chatSessions.length > 0) {
      const session = chatSessions.find(s => s.id === currentSessionId);
      if (session) {
        setMessages(session.messages);
      }
    }
  }, [currentSessionId, chatSessions]);

  // Persist currentSessionId to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('currentSessionId', currentSessionId === null ? 'null' : currentSessionId);
  }, [currentSessionId]);

  const loadChatHistory = () => {
    try {
      const savedSessions = localStorage.getItem('chatSessions');
      if (savedSessions) {
        const sessions = JSON.parse(savedSessions);
        setChatSessions(sessions);
        // Remove auto-selecting most recent session here
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  const saveChatHistory = useCallback((sessions: ChatSession[]) => {
    try {
      localStorage.setItem('chatSessions', JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving chat history:', error);
    }
  }, []);

  // Refactored createNewSession: Only clears state, does not add to chatSessions
  const createNewSession = () => {
    setCurrentSessionId(null);
    setMessages([]);
    setAttachedFiles([]);
    // Persist home state
    localStorage.setItem('currentSessionId', 'null');
  };

  // Refactored sendMessage: Create and save a new session if none exists
  const sendMessage = async (content: string, files?: FileAttachment[]) => {
    if (!content.trim() && (!files || files.length === 0)) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      isUser: true,
      timestamp: new Date(),
      files: files || attachedFiles,
    };

    let sessionId = currentSessionId;
    let updatedSessions = chatSessions;
    let isNewSession = false;

    // If no session, create a new one and add to chatSessions
    if (!sessionId) {
      const newSession: ChatSession = {
        id: Date.now().toString(),
        title: content.slice(0, 50) + '...',
        messages: [userMessage],
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      updatedSessions = [newSession, ...chatSessions];
      setChatSessions(updatedSessions);
      setCurrentSessionId(newSession.id);
      setMessages([userMessage]);
      setAttachedFiles([]);
      saveChatHistory(updatedSessions);
      sessionId = newSession.id;
      isNewSession = true;
    } else {
      const updatedMessages = [...messages, userMessage];
      setMessages(updatedMessages);
      setAttachedFiles([]);
      updatedSessions = chatSessions.map(session =>
        session.id === sessionId
          ? {
              ...session,
              messages: updatedMessages,
              updatedAt: new Date(),
              title: session.messages.length === 0 ? content.slice(0, 50) + '...' : session.title
            }
          : session
      );
      setChatSessions(updatedSessions);
      saveChatHistory(updatedSessions);
    }

    setIsLoading(true);

    try {
      // Get user info from localStorage
      let userEmail = '<EMAIL>';
      let userEmployeeId = 'EMP123';
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const userObj = JSON.parse(userStr);
          if (userObj.email) userEmail = userObj.email;
          if (userObj.employeeId) userEmployeeId = userObj.employeeId;
        }
      } catch (e) {
        // fallback to defaults
      }
      // Call the real backend API with all required fields
      const response = await chatAPI.sendMessage(
        content,
        files?.map(f => f.file).filter((f): f is File => f !== undefined),
        'web-client', // deviceId placeholder
        userEmail, // email from user
        userEmployeeId, // employeeId from user
        sessionId // chatId
      );
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response && typeof response === 'object' && 'response' in response && typeof response.response === 'string' ? response.response : "No response from server.",
        isUser: false,
        timestamp: new Date(),
      };
      const finalMessages = isNewSession ? [userMessage, botMessage] : [...messages, userMessage, botMessage];
      setMessages(finalMessages);
      // Update current session
      const finalSessions = updatedSessions.map(session =>
        session.id === sessionId
          ? {
              ...session,
              messages: finalMessages,
              updatedAt: new Date(),
              title: session.messages.length <= 1 ? content.slice(0, 50) + '...' : session.title
            }
          : session
      );
      setChatSessions(finalSessions);
      saveChatHistory(finalSessions);
    } catch (error) {
      console.error('Error sending message:', error);
      // Optionally, show error to user
    } finally {
      setIsLoading(false);
    }
  };

  const loadSession = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSessionId(sessionId);
      setMessages(session.messages);
      setAttachedFiles([]);
      // Persist selected session
      localStorage.setItem('currentSessionId', sessionId);
    }
  };

  const deleteSession = (sessionId: string) => {
    const updatedSessions = chatSessions.filter(s => s.id !== sessionId);
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);

    if (currentSessionId === sessionId) {
      if (updatedSessions.length > 0) {
        loadSession(updatedSessions[0].id);
      } else {
        createNewSession();
      }
    }
  };

  const archiveSession = (sessionId: string) => {
    const updatedSessions = chatSessions.map(session =>
      session.id === sessionId ? { ...session, isArchived: true } : session
    );
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);
  };

  const addFileAttachment = (file: FileAttachment) => {
    setAttachedFiles(prev => [...prev, file]);
  };

  const removeFileAttachment = (fileId: string) => {
    setAttachedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearAllChats = () => {
    setChatSessions([]);
    setMessages([]);
    setCurrentSessionId(null);
    setAttachedFiles([]);
    localStorage.removeItem('chatSessions');
    localStorage.setItem('currentSessionId', 'null');
    createNewSession();
  };

  const renameSession = (sessionId: string, newTitle: string) => {
    const updatedSessions = chatSessions.map(session =>
      session.id === sessionId ? { ...session, title: newTitle, updatedAt: new Date() } : session
    );
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);
  };

  const downloadSession = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (!session) return;
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(session, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `${session.title || 'chat'}-${session.id}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  return {
    messages,
    chatSessions: chatSessions.filter(s => !s.isArchived),
    archivedSessions: chatSessions.filter(s => s.isArchived),
    currentSessionId,
    isLoading,
    attachedFiles,
    sendMessage,
    createNewSession,
    loadSession,
    deleteSession,
    archiveSession,
    addFileAttachment,
    removeFileAttachment,
    clearAllChats,
    renameSession,
    downloadSession,
  };
};
