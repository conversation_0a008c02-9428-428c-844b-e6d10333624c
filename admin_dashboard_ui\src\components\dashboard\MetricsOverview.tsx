import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  MessageSquare, 
  Clock, 
  Heart,
  AlertTriangle,
  CheckCircle,
  Activity,
  RefreshCw
} from 'lucide-react';
import { useDashboardMetrics } from '../../hooks/useQueries';
import { SkeletonCard, SkeletonChart } from '../ui/LoadingSkeleton';
import { formatNumber, formatPercentage, formatRelativeTime } from '../../i18n';
import { cn } from '../../lib/utils';

// ============================================================================
// TYPES
// ============================================================================

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
  isLoading?: boolean;
  onClick?: () => void;
}

interface MetricsOverviewProps {
  className?: string;
  showRefreshButton?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

// ============================================================================
// METRIC CARD COMPONENT
// ============================================================================

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  color = 'primary',
  isLoading = false,
  onClick,
}) => {
  const { t } = useTranslation();

  const colorClasses = {
    primary: 'border-primary/20 bg-primary/5 text-primary',
    success: 'border-success-500/20 bg-success-500/5 text-success-600',
    warning: 'border-warning-500/20 bg-warning-500/5 text-warning-600',
    error: 'border-error-500/20 bg-error-500/5 text-error-600',
    info: 'border-info-500/20 bg-info-500/5 text-info-600',
  };

  const changeColorClasses = {
    increase: 'text-success-600 bg-success-100',
    decrease: 'text-error-600 bg-error-100',
    neutral: 'text-muted-foreground bg-muted',
  };

  if (isLoading) {
    return <SkeletonCard className="h-32" />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2, scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "relative p-6 border rounded-lg bg-card cursor-pointer group",
        "hover:shadow-apple-md transition-all duration-200",
        onClick && "hover:border-primary/30"
      )}
      onClick={onClick}
    >
      {/* Background gradient */}
      <div className={cn(
        "absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200",
        colorClasses[color]
      )} />
      
      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className={cn(
            "p-2 rounded-lg",
            colorClasses[color]
          )}>
            {icon}
          </div>
          
          {change !== undefined && (
            <div className={cn(
              "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium",
              changeColorClasses[changeType]
            )}>
              {changeType === 'increase' ? (
                <TrendingUp className="h-3 w-3" />
              ) : changeType === 'decrease' ? (
                <TrendingDown className="h-3 w-3" />
              ) : null}
              <span>{formatPercentage(Math.abs(change))}</span>
            </div>
          )}
        </div>
        
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground">
            {typeof value === 'number' ? formatNumber(value) : value}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

// ============================================================================
// METRICS OVERVIEW COMPONENT
// ============================================================================

export const MetricsOverview: React.FC<MetricsOverviewProps> = ({
  className,
  showRefreshButton = true,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
}) => {
  const { t } = useTranslation();
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  
  const {
    data: metrics,
    isLoading,
    error,
    refetch,
    isFetching
  } = useDashboardMetrics();

  useEffect(() => {
    if (metrics) {
      setLastRefresh(new Date());
    }
  }, [metrics]);

  // Manual refresh handler
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh countdown
  const [countdown, setCountdown] = useState(refreshInterval / 1000);
  
  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          return refreshInterval / 1000;
        }
        return prev - 1;
      });
    }, 1000);
    
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  if (error) {
    return (
      <div className={cn("p-6 border border-error-200 rounded-lg bg-error-50", className)}>
        <div className="flex items-center space-x-3">
          <AlertTriangle className="h-5 w-5 text-error-600" />
          <div>
            <h3 className="font-medium text-error-900">{t('errors.error_loading')}</h3>
            <p className="text-sm text-error-700">{error.message}</p>
          </div>
        </div>
        <button
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-error-600 text-white rounded-md hover:bg-error-700 transition-colors"
        >
          {t('common.refresh')}
        </button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">
            {t('dashboard.quick_stats')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('dashboard.last_updated')}: {formatRelativeTime(lastRefresh)}
          </p>
        </div>
        
        {showRefreshButton && (
          <div className="flex items-center space-x-3">
            {autoRefresh && (
              <span className="text-xs text-muted-foreground">
                {t('common.refresh')} in {countdown}s
              </span>
            )}
            <button
              onClick={handleRefresh}
              disabled={isFetching}
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium",
                "bg-primary text-primary-foreground hover:bg-primary/90",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "transition-colors duration-200 focus-ring"
              )}
            >
              <RefreshCw className={cn("h-4 w-4", isFetching && "animate-spin")} />
              <span>{t('common.refresh')}</span>
            </button>
          </div>
        )}
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title={t('dashboard.total_users')}
          value={metrics?.active_users || 0}
          change={12.5}
          changeType="increase"
          icon={<Users className="h-5 w-5" />}
          color="primary"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('dashboard.total_queries')}
          value={metrics?.total_queries || 0}
          change={8.2}
          changeType="increase"
          icon={<MessageSquare className="h-5 w-5" />}
          color="info"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('dashboard.avg_response_time')}
          value={metrics?.avg_chat_duration ? `${metrics.avg_chat_duration}s` : '0s'}
          change={-5.1}
          changeType="decrease"
          icon={<Clock className="h-5 w-5" />}
          color="success"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('dashboard.user_satisfaction')}
          value={metrics?.user_satisfaction ? formatPercentage(metrics.user_satisfaction) : '0%'}
          change={3.7}
          changeType="increase"
          icon={<Heart className="h-5 w-5" />}
          color="warning"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('dashboard.resolution_rate')}
          value={metrics?.resolution_rate ? formatPercentage(metrics.resolution_rate) : '0%'}
          change={2.1}
          changeType="increase"
          icon={<CheckCircle className="h-5 w-5" />}
          color="success"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('dashboard.escalation_rate')}
          value={metrics?.escalation_rate ? formatPercentage(metrics.escalation_rate) : '0%'}
          change={-1.3}
          changeType="decrease"
          icon={<AlertTriangle className="h-5 w-5" />}
          color="error"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('dashboard.uptime')}
          value={metrics?.uptime_percentage ? formatPercentage(metrics.uptime_percentage) : '0%'}
          change={0.1}
          changeType="increase"
          icon={<Activity className="h-5 w-5" />}
          color="primary"
          isLoading={isLoading}
        />
        
        <MetricCard
          title={t('metrics.error_rates')}
          value={metrics?.error_rate ? formatPercentage(metrics.error_rate) : '0%'}
          change={-0.8}
          changeType="decrease"
          icon={<AlertTriangle className="h-5 w-5" />}
          color="info"
          isLoading={isLoading}
        />
      </div>

      {/* Real-time indicator */}
      <AnimatePresence>
        {isFetching && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed bottom-4 right-4 z-50"
          >
            <div className="flex items-center space-x-2 px-3 py-2 bg-primary text-primary-foreground rounded-full shadow-lg">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span className="text-sm font-medium">{t('dashboard.loading_metrics')}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MetricsOverview;
