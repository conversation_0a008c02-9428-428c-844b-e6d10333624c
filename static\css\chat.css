/* 
 * Chat and Message Styles
 * Contains styles for chat messages, greeting, suggestions, and conversation elements
 */

/* Greeting Message Container */
.greeting-message-container {
    position: static !important;
    margin-bottom: 8px !important;
    margin-top: 0 !important;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

/* Ensure greeting message container is visible for logged-in users when chat is empty */
#greetingMessageContainer {
    display: block !important;
    text-align: center;
    margin: 0 auto;
}

/* Hide greeting message container only when chat has messages */
.chat-messages.has-messages #greetingMessageContainer {
    display: none !important;
}

/* Greeting Message Styling */
.greeting-message {
    color: var(--text-primary, #000000) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    margin: 0 0 16px 0 !important;
    text-align: center !important;
}

/* Welcome Message Box */
.greeting-message-container .welcome-message h2 {
    margin-bottom: 16px !important;
    color: var(--text-primary, #000000) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
}

.greeting-message-container .welcome-message p {
    margin-bottom: 24px !important;
    color: var(--text-secondary, #666666) !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
}

/* Suggestion Chips Container */
.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 12px !important;
    justify-content: center;
    margin-top: 16px !important;
}

/* Premium Suggestion Chip Styling */
.suggestion-chip {
    background-color: var(--bg-primary, #FFFFFF);
    color: var(--text-primary, #000000);
    border: 1px solid var(--border-color, #E5E5E5);
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Shimmer effect for premium feel */
.suggestion-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

/* Premium hover effects */
.suggestion-chip:hover {
    background-color: rgba(0, 0, 0, 0.08) !important;
    border-color: var(--text-secondary, #666666) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.suggestion-chip:hover::before {
    left: 100%;
}

/* Active state for button press */
.suggestion-chip:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
    background-color: rgba(0, 0, 0, 0.12) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Suggestion chip icons */
.suggestion-chip i, .suggestion-chip svg, .suggestion-chip img {
    width: 18px !important;
    height: 18px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 8px !important;
}

/* Chat History Styles */
.chat-history-header {
    position: sticky;
    top: 32px;
    z-index: 25;
    background: #fafafa !important;
    box-shadow: 0 6px 12px -2px rgba(0,0,0,0.10);
    padding: 8px 16px;
    margin: 0 -16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--sidebar-text);
    opacity: 0.7;
    border-radius: 0;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    font-weight: bold;
    color: #111 !important;
    opacity: 1 !important;
}

.chat-history-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: #fafafa !important;
}

.chat-history-header span.chat-history-header-text {
    position: relative;
    z-index: 2;
    background: transparent;
    color: var(--sidebar-text);
    opacity: 0.8;
    font-weight: bold;
    color: #111 !important;
    opacity: 1 !important;
}

/* Chat History List */
.chat-history-list {
    padding: 0;
    margin-bottom: 0;
}

/* Chat History Items */
.chat-history-item {
    padding: 6px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 2px 8px;
    color: var(--sidebar-text);
    transition: background-color 0.2s ease;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    border: 1px solid transparent;
}

.chat-history-item:hover {
    background-color: var(--sidebar-item-hover);
}

.chat-history-item.active {
    background-color: var(--sidebar-item-active);
    font-weight: 500;
}

.chat-history-item-content {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 8px;
}

.chat-history-item-title {
    font-weight: 500;
    position: relative;
    color: var(--sidebar-text);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 180px;
}

.chat-history-item.active .chat-history-item-title {
    font-weight: 700;
}

/* Chat History Item Menu */
.chat-history-item-menu {
    color: var(--sidebar-text);
    opacity: 0;
    font-size: 14px;
    padding: 4px;
    cursor: pointer;
    transition: opacity 0.2s ease;
    border-radius: 4px;
}

.chat-history-item:hover .chat-history-item-menu,
.chat-history-item-menu:hover,
.chat-history-item-menu:focus {
    opacity: 0.8;
}

/* Extra: ensure chat items have margin below header */
.chat-history-header + .chat-history-item {
    margin-top: 2px;
}

/* Message Content Styles */
.message-content {
    color: var(--message-text);
    font-size: 1rem;
    line-height: 1.6;
    font-weight: 400;
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 12px;
}

.bot-message .message-content {
    max-width: 95%;
    background-color: var(--message-bot-bg);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(0, 0, 0, 0.03);
    margin-left: 0;
    color: var(--text-primary);
}

.user-message .message-content {
    background-color: var(--message-user-bg);
    color: var(--text-primary);
    border-radius: 12px 12px 0 12px;
    box-shadow: var(--card-shadow);
}

/* Message Footer */
.message-footer {
    max-width: 85%;
    margin: 4px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.bot-message .message-footer {
    max-width: 95%;
    justify-content: space-between;
    align-items: center;
    padding-left: 0;
    margin-left: 0;
}

.user-message .message-footer {
    justify-content: flex-end;
}

.message-time {
    font-size: 12px;
    color: var(--text-secondary);
    margin-left: 8px;
    white-space: nowrap;
    font-weight: 400;
    opacity: 0.8;
}

/* Message Feedback Buttons */
.message-feedback {
    display: flex;
    gap: 8px;
    margin-right: 12px;
    position: relative;
    z-index: 2;
}

.message-feedback .feedback-btn {
    background-color: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-feedback .feedback-btn:hover {
    opacity: 1;
    background-color: var(--feedback-hover-bg);
}

.message-feedback .feedback-btn:active {
    transform: scale(0.95);
}

/* Ensure icons scale with the button */
.message-feedback .feedback-btn i {
    pointer-events: none;
}
